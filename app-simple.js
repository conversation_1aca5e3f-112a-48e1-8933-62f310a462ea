require('dotenv').config();

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');

const app = express();
const port = process.env.PORT || 3001;

// --- Middlewares ---
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Health check endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Video Backend API',
    version: '1.0.0',
    status: 'running'
  });
});

// Import routes
const resourceRoutes = require('./src/routes/resourceRoutes');
const projectRoutes = require('./src/routes/projectRoutes');
const scriptRoutes = require('./src/routes/scriptRoutes');
const taskRoutes = require('./src/routes/taskRoutes');
const voiceRoutes = require('./src/routes/voiceRoutes');
const videoRoutes = require('./src/routes/videoRoutes');
const avatarRoutes = require('./src/routes/avatarRoutes');

// API Routes
app.use('/api/resources', resourceRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/scripts', scriptRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/voice', voiceRoutes);
app.use('/api/video', videoRoutes);
app.use('/api/avatar', avatarRoutes);

// Simple test routes
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'API is working!'
  });
});

// --- Server Startup ---
app.listen(port, () => {
  console.log(`🚀 Video Backend API listening on port ${port}`);
  console.log(`📝 Environment: ${process.env.NODE_ENV}`);
  console.log(`🌐 API Base URL: ${process.env.API_BASE_URL}`);
});
