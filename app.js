require('dotenv').config();

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');

// Import routes
const projectRoutes = require('./src/routes/projectRoutes');
const taskRoutes = require('./src/routes/taskRoutes');
const resourceRoutes = require('./src/routes/resourceRoutes');
const scriptRoutes = require('./src/routes/scriptRoutes');
const voiceRoutes = require('./src/routes/voiceRoutes');
const videoRoutes = require('./src/routes/videoRoutes');
const avatarRoutes = require('./src/routes/avatarRoutes');

const app = express();
const port = process.env.PORT || 3001;

// --- Middlewares ---
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use(express.static(path.join(__dirname, 'public')));

// --- API Routes ---
app.use('/api/projects', projectRoutes);
app.use('/api/resources', resourceRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/scripts', scriptRoutes);
app.use('/api/voice', voiceRoutes);
app.use('/api/video', videoRoutes);
app.use('/api/avatar', avatarRoutes);

// Health check endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Video Backend API',
    version: '1.0.0',
    environment: process.env.NODE_ENV,
    status: 'running'
  });
});

// Config endpoint
app.get('/config', (req, res) => {
  res.json({
    nodeEnv: process.env.NODE_ENV,
    port: process.env.PORT,
    apiBaseUrl: process.env.API_BASE_URL,
    uploadPath: process.env.UPLOAD_PATH,
    maxFileSize: process.env.MAX_FILE_SIZE
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// --- Server Startup ---
app.listen(port, () => {
  console.log(`🚀 Video Backend API listening on port ${port}`);
});