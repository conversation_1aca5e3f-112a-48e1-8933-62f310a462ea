<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Backend API - 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Video Backend API 测试页面</h1>
        
        <div class="grid">
            <div class="section">
                <h2>🔍 健康检查</h2>
                <button onclick="testHealthCheck()">测试健康检查</button>
                <div id="health-result" class="result"></div>
            </div>

            <div class="section">
                <h2>📁 项目管理</h2>
                <input type="text" id="project-name" placeholder="项目名称" value="测试项目">
                <button onclick="createProject()">创建项目</button>
                <div id="project-result" class="result"></div>
            </div>

            <div class="section">
                <h2>🔊 声音资源</h2>
                <button onclick="getVoices()">获取可用声音</button>
                <div id="voices-result" class="result"></div>
            </div>

            <div class="section">
                <h2>👤 头像资源</h2>
                <button onclick="getAvatars()">获取可用头像</button>
                <div id="avatars-result" class="result"></div>
            </div>

            <div class="section">
                <h2>📝 脚本生成</h2>
                <textarea id="script-text" placeholder="输入文本内容..." rows="3">这是一个关于人工智能技术发展的视频内容，我们将探讨AI在各个领域的应用和未来发展趋势。</textarea>
                <button onclick="generateScripts()">生成脚本</button>
                <div id="scripts-result" class="result"></div>
            </div>

            <div class="section">
                <h2>🎤 配音生成</h2>
                <input type="text" id="voice-text" placeholder="要转换的文本" value="这是一段测试配音文本">
                <select id="voice-type">
                    <option value="professional-female">专业女声</option>
                    <option value="professional-male">专业男声</option>
                    <option value="friendly-female">亲和女声</option>
                    <option value="energetic-male">活力男声</option>
                </select>
                <button onclick="generateVoice()">生成配音</button>
                <div id="voice-result" class="result"></div>
            </div>
        </div>

        <div class="section">
            <h2>📊 API状态</h2>
            <p>服务器地址: <strong>http://localhost:3001</strong></p>
            <p>API文档: <a href="/API_DOCUMENTATION.md" target="_blank">查看完整API文档</a></p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001';

        async function apiCall(endpoint, options = {}) {
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                const data = await response.json();
                return { success: response.ok, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(result, null, 2);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
        }

        async function testHealthCheck() {
            const result = await apiCall('/');
            displayResult('health-result', result.data, result.success);
        }

        async function createProject() {
            const name = document.getElementById('project-name').value;
            const result = await apiCall('/api/projects', {
                method: 'POST',
                body: JSON.stringify({ name })
            });
            displayResult('project-result', result.data, result.success);
        }

        async function getVoices() {
            const result = await apiCall('/api/resources/voices');
            displayResult('voices-result', result.data, result.success);
        }

        async function getAvatars() {
            const result = await apiCall('/api/resources/avatars');
            displayResult('avatars-result', result.data, result.success);
        }

        async function generateScripts() {
            const textMaterial = document.getElementById('script-text').value;
            const result = await apiCall('/api/scripts/generate', {
                method: 'POST',
                body: JSON.stringify({
                    projectName: '测试项目',
                    materialSource: 'text',
                    textMaterial
                })
            });
            displayResult('scripts-result', result.data, result.success);
        }

        async function generateVoice() {
            const text = document.getElementById('voice-text').value;
            const voiceId = document.getElementById('voice-type').value;
            const result = await apiCall('/api/voice/generate', {
                method: 'POST',
                body: JSON.stringify({
                    shotId: 'test-shot-' + Date.now(),
                    text,
                    voiceId
                })
            });
            displayResult('voice-result', result.data, result.success);
        }

        // 页面加载时自动测试健康检查
        window.onload = function() {
            testHealthCheck();
        };
    </script>
</body>
</html>
