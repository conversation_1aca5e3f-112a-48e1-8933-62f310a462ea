# Video Backend API

一个功能完整的视频生成后端API，支持从内容上传到最终视频导出的完整工作流程。

## 🚀 功能特性

### 核心功能
- **项目管理** - 创建、更新和管理视频项目
- **智能脚本生成** - 基于上传内容或文本材料自动生成视频脚本
- **镜头编辑** - 将脚本转换为具体的视频镜头
- **AI配音生成** - 多种声音类型的文本转语音功能
- **视频生成** - 多种风格的视频片段生成
- **数字人集成** - 虚拟主播和数字人角色
- **视频合成** - 将多个镜头合成为完整视频
- **任务管理** - 异步任务处理和进度跟踪

### 技术特性
- RESTful API设计
- 异步任务处理
- 文件上传支持
- 错误处理和日志记录
- 环境配置管理
- CORS支持

## 📋 API端点

### 基础端点
- `GET /` - 健康检查
- `GET /config` - 配置信息

### 项目管理
- `POST /api/projects` - 创建项目
- `GET /api/projects/:projectId` - 获取项目
- `PUT /api/projects/:projectId` - 更新项目
- `POST /api/projects/:projectId/assets` - 上传资源

### 脚本生成
- `POST /api/scripts/generate` - 生成脚本
- `POST /api/scripts/regenerate` - 重新生成脚本
- `PUT /api/scripts/:scriptId` - 更新脚本
- `DELETE /api/scripts/:scriptId` - 删除脚本
- `POST /api/scripts/shots/generate` - 生成镜头
- `POST /api/scripts/shots/regenerate` - 重新生成镜头

### 配音生成
- `POST /api/voice/generate` - 生成配音
- `POST /api/voice/generate-batch` - 批量生成配音
- `GET /api/voice/preview/:voiceId` - 配音预览
- `POST /api/voice/synthesize` - 文本转语音

### 视频生成
- `POST /api/video/generate` - 生成视频
- `POST /api/video/generate-batch` - 批量生成视频
- `POST /api/video/compose` - 合成视频
- `GET /api/video/status/:taskId` - 获取生成状态
- `POST /api/video/export` - 导出视频

### 数字人/头像
- `GET /api/avatar/list` - 获取可用头像
- `POST /api/avatar/configure` - 配置头像
- `POST /api/avatar/generate` - 生成头像视频
- `GET /api/avatar/preview/:avatarId` - 头像预览
- `POST /api/avatar/customize` - 自定义头像

### 资源管理
- `GET /api/resources/avatars` - 获取头像列表
- `GET /api/resources/voices` - 获取声音列表

### 任务管理
- `POST /api/tasks` - 创建任务
- `GET /api/tasks/:taskId` - 获取任务状态
- `GET /api/tasks/project/:projectId` - 获取项目任务
- `DELETE /api/tasks/:taskId` - 取消任务

## 🛠️ 安装和运行

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 环境配置
创建 `.env` 文件：
```env
PORT=3001
NODE_ENV=development
API_BASE_URL=http://localhost:3001
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# 数据库配置（可选）
DB_HOST=localhost
DB_PORT=5432
DB_NAME=video_backend
DB_USER=admin
DB_PASSWORD=password123

# JWT配置（可选）
JWT_SECRET=your-secret-key-here
JWT_EXPIRES_IN=24h
```

### 启动服务器
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

### 运行测试
```bash
npm test
```

## 📁 项目结构

```
video-backend/
├── src/
│   ├── controllers/          # 控制器
│   │   ├── projectController.js
│   │   ├── scriptController.js
│   │   ├── voiceController.js
│   │   ├── videoController.js
│   │   ├── avatarController.js
│   │   ├── resourceController.js
│   │   └── taskController.js
│   ├── routes/              # 路由
│   │   ├── projectRoutes.js
│   │   ├── scriptRoutes.js
│   │   ├── voiceRoutes.js
│   │   ├── videoRoutes.js
│   │   ├── avatarRoutes.js
│   │   ├── resourceRoutes.js
│   │   └── taskRoutes.js
│   ├── middleware/          # 中间件
│   │   └── upload.js
│   └── db/                  # 数据存储
│       └── index.js
├── uploads/                 # 上传文件目录
├── app.js                   # 主应用文件
├── test-api.js             # API测试脚本
├── package.json
├── .env                    # 环境配置
└── README.md
```

## 🎯 使用示例

### 创建项目
```bash
curl -X POST http://localhost:3001/api/projects \
  -H "Content-Type: application/json" \
  -d '{"name": "我的视频项目"}'
```

### 生成脚本
```bash
curl -X POST http://localhost:3001/api/scripts/generate \
  -H "Content-Type: application/json" \
  -d '{
    "projectName": "我的视频项目",
    "materialSource": "text",
    "textMaterial": "这是视频内容..."
  }'
```

### 生成配音
```bash
curl -X POST http://localhost:3001/api/voice/generate \
  -H "Content-Type: application/json" \
  -d '{
    "shotId": "shot_123",
    "text": "这是要转换为语音的文本",
    "voiceId": "professional-female"
  }'
```

## 🔧 可用资源

### 声音类型
- `professional-female` - 专业女声
- `professional-male` - 专业男声
- `friendly-female` - 亲和女声
- `energetic-male` - 活力男声

### 头像类型
- `emma` - 艾玛（专业商务女性）
- `david` - 大卫（成熟商务男性）
- `sophia` - 索菲亚（年轻活泼女性）
- `alex` - 亚历克斯（创意年轻男性）

### 视频风格
- `cinematic` - 电影风格
- `documentary` - 纪录片风格
- `commercial` - 商业广告风格
- `artistic` - 艺术风格

## 📝 开发说明

这个后端API是为视频生成前端应用设计的，采用前端优先的开发方式。所有的API端点都是基于前端需求设计的，确保前后端的完美配合。

### 主要特点
- 模拟AI处理时间，提供真实的用户体验
- 支持异步任务处理，适合长时间的视频生成操作
- 提供丰富的配置选项和自定义功能
- 完整的错误处理和状态管理

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License
