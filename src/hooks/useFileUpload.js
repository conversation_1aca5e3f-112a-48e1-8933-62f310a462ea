import { useState, useCallback } from 'react';
import { useApp } from '../context/AppContext.jsx';
import { createFileObject, validateFileSize } from '../utils/fileUtils';

export function useFileUpload() {
  const { state, dispatch, ActionTypes } = useApp();
  const [isDragOver, setIsDragOver] = useState(false);

  const addFile = useCallback((file) => {
    // Validate file
    if (!validateFileSize(file, 50)) { // 50MB limit
      alert('文件大小不能超过50MB');
      return false;
    }

    const fileObject = createFileObject(file);
    dispatch({ type: ActionTypes.ADD_UPLOADED_FILE, payload: fileObject });
    return true;
  }, [dispatch, ActionTypes]);

  const removeFile = useCallback((fileId) => {
    dispatch({ type: ActionTypes.REMOVE_UPLOADED_FILE, payload: fileId });
  }, [dispatch, ActionTypes]);

  const handleFileSelect = useCallback((files) => {
    Array.from(files).forEach(file => {
      addFile(file);
    });
  }, [addFile]);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  return {
    uploadedFiles: state.uploadedFiles,
    isDragOver,
    addFile,
    removeFile,
    handleFileSelect,
    handleDragOver,
    handleDragLeave,
    handleDrop
  };
}
