// File utility functions

export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function generateFileId() {
  return Date.now() + Math.random().toString(36).substr(2, 9);
}

export function createFileObject(file) {
  return {
    id: generateFileId(),
    name: file.name,
    size: file.size,
    type: file.type,
    formattedSize: formatFileSize(file.size),
    file: file
  };
}

export function validateFileType(file, allowedTypes = []) {
  if (allowedTypes.length === 0) return true;
  return allowedTypes.some(type => file.type.includes(type));
}

export function validateFileSize(file, maxSizeInMB = 10) {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  return file.size <= maxSizeInBytes;
}
