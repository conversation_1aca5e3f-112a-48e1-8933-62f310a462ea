// Hash utility functions

export function simpleHash(str) {
  let hash = 0;
  if (str.length === 0) return hash;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash |= 0; // Convert to 32bit integer
  }
  return Math.abs(hash);
}

export function generateImageSeed(text, customPrompt = '') {
  const combinedText = `${text} ${customPrompt}`.trim();
  return simpleHash(combinedText);
}

export function generateImageUrl(seed, width = 400, height = 225) {
  return `https://picsum.photos/seed/${seed}/${width}/${height}`;
}
