const { nanoid } = require('nanoid');
const { tasks, projects } = require('../db');

const createTask = (req, res) => {
    const { projectId } = req.body;
  
    if (!projects[projectId]) {
      return res.status(404).json({ message: 'Project not found' });
    }
    
    const taskId = `task_${nanoid()}`;
    const newTask = {
      id: taskId,
      projectId,
      status: 'processing',
      progress: 0,
      createdAt: new Date().toISOString(),
    };
    tasks[taskId] = newTask;
    
    // Simulate async video rendering
    console.log(`Starting video render task: ${taskId} for project: ${projectId}`);
    let progress = 0;
    const interval = setInterval(() => {
      progress += 25;
      if (tasks[taskId]) {
        tasks[taskId].progress = progress;
        if (progress >= 100) {
          clearInterval(interval);
          tasks[taskId].status = 'completed';
          tasks[taskId].result = {
            videoUrl: `/videos/final_${projectId}.mp4`
          };
          console.log(`Task ${taskId} completed.`);
        }
      }
    }, 2000); // Update progress every 2 seconds
    
    res.status(202).json({ taskId });
};

const getTaskById = (req, res) => {
    const { taskId } = req.params;
    const task = tasks[taskId];
    
    if (task) {
      res.json(task);
    } else {
      res.status(404).json({ message: 'Task not found' });
    }
};

module.exports = {
    createTask,
    getTaskById,
};