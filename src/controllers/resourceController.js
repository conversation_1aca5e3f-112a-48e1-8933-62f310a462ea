const avatars = [
  { id: 'avatar_1', name: '晓晓', previewUrl: '/avatars/xiao.png' },
  { id: 'avatar_2', name: '明哥', previewUrl: '/avatars/ming.png' },
  { id: 'avatar_3', name: '丽莎', previewUrl: '/avatars/lisa.png' },
];

const voices = [
  { id: 'voice_1', name: '标准女声', lang: 'zh-CN', gender: 'female', previewUrl: '/voices/female_1.mp3' },
  { id: 'voice_2', name: '标准男声', lang: 'zh-CN', gender: 'male', previewUrl: '/voices/male_1.mp3' },
  { id: 'voice_3', name: '活泼女声', lang: 'zh-CN', gender: 'female', previewUrl: '/voices/female_2.mp3' },
];

const getAvatars = (req, res) => {
  res.json(avatars);
};

const getVoices = (req, res) => {
  res.json(voices);
};

module.exports = {
  getAvatars,
  getVoices,
};