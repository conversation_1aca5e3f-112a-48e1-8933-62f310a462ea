const express = require('express');
const router = express.Router();
const projectController = require('../controllers/projectController');
const upload = require('../middleware/upload');

// POST /api/projects - Create a new project
router.post('/', projectController.createProject);

// GET /api/projects/:projectId - Get a project by ID
router.get('/:projectId', projectController.getProjectById);

// PUT /api/projects/:projectId - Update a project
router.put('/:projectId', projectController.updateProject);

// POST /api/projects/:projectId/assets - Upload an asset
router.post('/:projectId/assets', upload.single('asset'), projectController.uploadAsset);

module.exports = router;
