import { useNavigation } from '../../hooks/useNavigation';

const ProgressSteps = () => {
  const { tabs, getCompletedSteps } = useNavigation();
  const completedSteps = getCompletedSteps();

  return (
    <div className="flex gap-5">
      {tabs.map((tab, index) => {
        const isCompleted = completedSteps.includes(tab.id);
        
        return (
          <div key={tab.id} className="flex items-center gap-2">
            <div
              className={`
                w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium
                ${isCompleted 
                  ? 'bg-primary text-white' 
                  : 'bg-gray-200 text-gray-500'
                }
              `}
            >
              {index + 1}
            </div>
            <div
              className={`
                text-sm
                ${isCompleted 
                  ? 'text-primary font-medium' 
                  : 'text-gray-500'
                }
              `}
            >
              {tab.label}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ProgressSteps;
