import { useState, useEffect } from 'react';
import Modal from './Modal';

const AvatarPreviewModal = ({ isOpen, onClose, avatarData, title }) => {
  const [isLoading, setIsLoading] = useState(true);

  // 处理ESC键关闭
  useEffect(() => {
    const handleEsc = (event) => {
      if (event.keyCode === 27) {
        onClose();
      }
    };
    
    if (isOpen) {
      document.addEventListener('keydown', handleEsc);
    }
    
    return () => {
      document.removeEventListener('keydown', handleEsc);
    };
  }, [isOpen, onClose]);

  // 模拟加载过程
  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);


  if (!isOpen || !avatarData) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title || "数字人预览"}
      size="lg"
      className="max-h-[90vh]"
    >
      <div className="space-y-6">
        {/* Loading State */}
        {isLoading && (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="w-16 h-16 border-4 border-gray-200 border-t-primary rounded-full animate-spin mb-4"></div>
            <p className="text-gray-600">正在加载数字人预览...</p>
          </div>
        )}

        {/* Preview Content */}
        {!isLoading && (
          <>
            {/* Avatar Preview */}
            <div className="relative bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg overflow-hidden">
              <div className="aspect-video flex items-center justify-center p-8">
                <div className="relative">
                  <img
                    src={avatarData.previewUrl || avatarData.imageUrl}
                    alt={avatarData.name}
                    className="w-64 h-80 object-cover rounded-lg shadow-lg"
                  />
                  
                  {/* Animation Indicator */}
                  <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm flex items-center gap-2">
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    演示中
                  </div>
                </div>
              </div>
            </div>

            {/* Avatar Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">数字人:</span>
                  <div className="font-medium text-gray-900">{avatarData.name}</div>
                </div>
                <div>
                  <span className="text-gray-500">位置:</span>
                  <div className="font-medium text-gray-900">
                    {avatarData.position === 'center' ? '居中' : 
                     avatarData.position === 'left' ? '左侧' : '右侧'}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500">大小:</span>
                  <div className="font-medium text-gray-900">
                    {avatarData.size === 'small' ? '小' : 
                     avatarData.size === 'medium' ? '中' : '大'}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500">表情:</span>
                  <div className="font-medium text-gray-900">
                    {avatarData.expression === 'neutral' ? '中性' : 
                     avatarData.expression === 'smile' ? '微笑' : '严肃'}
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default AvatarPreviewModal;
