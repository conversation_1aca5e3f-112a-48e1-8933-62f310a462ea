import { useEffect } from 'react';
import Modal from './Modal';

const ImagePreviewModal = ({ isOpen, onClose, imageUrl, title, description }) => {
  // 处理ESC键关闭
  useEffect(() => {
    const handleEsc = (event) => {
      if (event.keyCode === 27) {
        onClose();
      }
    };
    
    if (isOpen) {
      document.addEventListener('keydown', handleEsc);
    }
    
    return () => {
      document.removeEventListener('keydown', handleEsc);
    };
  }, [isOpen, onClose]);

  if (!isOpen || !imageUrl) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title || "图片预览"}
      size="lg"
      className="max-h-[90vh] overflow-hidden"
    >
      <div className="flex flex-col items-center">
        {/* Image Container */}
        <div className="relative w-full max-w-4xl bg-gray-50 rounded-lg overflow-hidden">
          <img
            src={imageUrl}
            alt={title || "预览图片"}
            className="w-full h-auto max-h-[70vh] object-contain"
            onError={(e) => {
              e.target.src = 'https://via.placeholder.com/800x600?text=图片加载失败';
            }}
          />
        </div>

        {/* Description */}
        {description && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg w-full">
            <p className="text-sm text-gray-600 leading-relaxed">
              {description}
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3 mt-6 w-full justify-center">
          <button
            onClick={() => {
              const link = document.createElement('a');
              link.href = imageUrl;
              link.download = `${title || 'image'}.jpg`;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            }}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200 flex items-center gap-2"
          >
            <span className="material-icons text-sm">download</span>
            下载图片
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors duration-200"
          >
            关闭
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default ImagePreviewModal;
