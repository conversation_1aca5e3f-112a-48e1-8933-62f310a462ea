import { useEffect } from 'react';
import { useApp } from '../../context/AppContext.jsx';
import { useApi } from '../../hooks/useApi.js';

const ApiStatus = () => {
  const { state } = useApp();
  const { checkApiHealth } = useApi();

  useEffect(() => {
    // 初始检查
    checkApiHealth();

    // 定期检查API状态
    const interval = setInterval(() => {
      checkApiHealth();
    }, 30000); // 每30秒检查一次

    return () => clearInterval(interval);
  }, [checkApiHealth]);

  if (state.apiConnected) {
    return (
      <div className="flex items-center text-sm text-green-600">
        <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
        API已连接
      </div>
    );
  }

  return (
    <div className="flex items-center text-sm text-red-600">
      <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
      API连接失败
    </div>
  );
};

export default ApiStatus;
