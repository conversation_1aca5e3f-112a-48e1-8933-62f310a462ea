import { useNavigation } from '../../hooks/useNavigation';
import VoiceSelector from './VoiceSelector';
import AvatarSelector from './AvatarSelector';
import PreviewPlayer from './PreviewPlayer';

const VoiceAndAvatar = () => {
  const { setCurrentTab } = useNavigation();

  const handleRegenerate = () => {
    console.log('Regenerating voice and avatar preview...');
    // Implement regeneration logic here
  };

  const handleConfirm = () => {
    setCurrentTab('export');
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          配音与数字人
        </h2>
        <p className="text-gray-600">
          选择合适的配音和数字人形象，打造专业的视频效果
        </p>
      </div>

      {/* Voice and Avatar Selection */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <VoiceSelector />
        <AvatarSelector />
      </div>

      {/* Preview Section */}
      <PreviewPlayer 
        onRegenerate={handleRegenerate}
        onConfirm={handleConfirm}
      />
    </div>
  );
};

export default VoiceAndAvatar;
