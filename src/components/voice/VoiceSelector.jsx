import { useApp } from '../../context/AppContext.jsx';

const VoiceSelector = () => {
  const { state, dispatch, ActionTypes } = useApp();

  const voices = [
    {
      id: 'professional-female',
      name: '专业女声',
      description: '清晰自然的女性声音'
    },
    {
      id: 'energetic-male',
      name: '活力男声',
      description: '充满能量的男性声音'
    },
    {
      id: 'gentle-child',
      name: '温柔童声',
      description: '天真可爱的儿童声音'
    },
    {
      id: 'news-anchor',
      name: '新闻主播',
      description: '权威正式的播报风格'
    }
  ];

  const handleVoiceSelect = (voiceId) => {
    dispatch({ type: ActionTypes.SET_SELECTED_VOICE, payload: voiceId });
  };

  const handlePlaySample = (e, voiceId) => {
    e.stopPropagation();
    console.log(`Playing voice sample for: ${voiceId}`);
    // Implement voice sample playback logic here
  };

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">配音设置</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {voices.map((voice) => {
          const isSelected = state.selectedVoice === voice.id;
          
          return (
            <div
              key={voice.id}
              onClick={() => handleVoiceSelect(voice.id)}
              className={`
                p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 hover:shadow-md hover:-translate-y-0.5
                ${isSelected 
                  ? 'border-primary bg-blue-50' 
                  : 'border-gray-200 bg-gray-50 hover:border-gray-300'
                }
              `}
            >
              <div className="flex items-start gap-3">
                <div className={`
                  w-12 h-12 rounded-full flex items-center justify-center
                  ${isSelected ? 'bg-blue-100' : 'bg-gray-100'}
                `}>
                  <span className={`
                    material-icons text-2xl
                    ${isSelected ? 'text-primary' : 'text-gray-500'}
                  `}>
                    record_voice_over
                  </span>
                </div>
                
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">
                    {voice.name}
                  </h4>
                  <p className="text-sm text-gray-600 mb-3">
                    {voice.description}
                  </p>
                  <button
                    onClick={(e) => handlePlaySample(e, voice.id)}
                    className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-full transition-colors duration-200"
                  >
                    试听
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default VoiceSelector;
