import Button from '../common/Button';

const PreviewPlayer = ({ onRegenerate, onConfirm }) => {
  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">效果预览</h3>
      
      {/* Video Player */}
      <div className="mb-6">
        <video
          controls
          loop
          autoPlay
          muted
          poster="https://images.unsplash.com/photo-1574717523213-2a6285a2b450?w=600&h=338&fit=crop&q=80"
          className="w-full rounded-lg"
        >
          <source 
            src="https://videos.pexels.com/video-files/853875/853875-hd_1280_720_25fps.mp4" 
            type="video/mp4" 
          />
          您的浏览器不支持视频播放。
        </video>
      </div>
      
      {/* Action Buttons */}
      <div className="flex justify-end gap-3">
        <Button 
          variant="secondary" 
          onClick={onRegenerate}
        >
          重新生成
        </Button>
        <Button 
          variant="primary" 
          onClick={onConfirm}
        >
          确认并生成完整视频
        </Button>
      </div>
    </div>
  );
};

export default PreviewPlayer;
