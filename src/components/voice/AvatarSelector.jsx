import { useApp } from '../../context/AppContext.jsx';

const AvatarSelector = () => {
  const { state, dispatch, ActionTypes } = useApp();

  const avatars = [
    {
      id: 'emma',
      name: '<PERSON>',
      description: '专业商务形象',
      imageUrl: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&q=80'
    },
    {
      id: 'david',
      name: '<PERSON>',
      description: '休闲亲和形象',
      imageUrl: 'https://picsum.photos/seed/david/150/150'
    },
    {
      id: 'chloe',
      name: 'Chloe',
      description: '时尚科技感',
      imageUrl: 'https://picsum.photos/seed/chloe/150/150'
    },
    {
      id: 'mark',
      name: '<PERSON>',
      description: '成熟稳重风格',
      imageUrl: 'https://picsum.photos/seed/mark/150/150'
    }
  ];

  const handleAvatarSelect = (avatarId) => {
    dispatch({ type: ActionTypes.SET_SELECTED_AVATAR, payload: avatarId });
  };

  const handlePreview = (e, avatarId) => {
    e.stopPropagation();
    console.log(`Previewing avatar: ${avatarId}`);
    // Implement avatar preview logic here
  };

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">数字人选择</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {avatars.map((avatar) => {
          const isSelected = state.selectedAvatar === avatar.id;
          
          return (
            <div
              key={avatar.id}
              onClick={() => handleAvatarSelect(avatar.id)}
              className={`
                p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 hover:shadow-md hover:-translate-y-0.5 text-center
                ${isSelected 
                  ? 'border-primary bg-blue-50' 
                  : 'border-gray-200 bg-gray-50 hover:border-gray-300'
                }
              `}
            >
              <img
                src={avatar.imageUrl}
                alt={avatar.name}
                className="w-24 h-24 rounded-full mx-auto mb-3 object-cover bg-gray-100"
              />
              
              <h4 className="font-medium text-gray-900 mb-1">
                {avatar.name}
              </h4>
              <p className="text-sm text-gray-600 mb-3">
                {avatar.description}
              </p>
              <button
                onClick={(e) => handlePreview(e, avatar.id)}
                className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-full transition-colors duration-200"
              >
                预览
              </button>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AvatarSelector;
