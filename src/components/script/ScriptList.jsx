import { useApp } from '../../context/AppContext.jsx';
import ScriptItem from './ScriptItem';

const ScriptList = () => {
  const { state } = useApp();

  if (state.scripts.length === 0) {
    return (
      <div className="text-center py-12">
        <span className="material-icons text-6xl text-gray-300 mb-4 block">
          description
        </span>
        <p className="text-gray-500 text-lg">
          暂无脚本内容，请先添加脚本条目
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {state.scripts.map((script, index) => (
        <ScriptItem
          key={script.id}
          script={script}
          index={index}
        />
      ))}
    </div>
  );
};

export default ScriptList;
