import { useState } from 'react';
import { useApp } from '../../context/AppContext.jsx';

const ScriptItem = ({ script, index }) => {
  const { dispatch, ActionTypes } = useApp();
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(script.text);

  const handleTextChange = (e) => {
    setEditText(e.target.value);
  };

  const handleTextBlur = () => {
    if (editText !== script.text) {
      dispatch({
        type: ActionTypes.UPDATE_SCRIPT,
        payload: { id: script.id, text: editText }
      });
    }
    setIsEditing(false);
  };

  const handleTextKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleTextBlur();
    }
    if (e.key === 'Escape') {
      setEditText(script.text);
      setIsEditing(false);
    }
  };

  const handleRegenerate = () => {
    // Simulate AI regeneration
    const newText = "这是一条由AI重新生成的、全新的、充满创意的脚本内容。";
    dispatch({
      type: ActionTypes.REGENERATE_SCRIPT,
      payload: { id: script.id, newText }
    });
  };

  const handleDelete = () => {
    dispatch({
      type: ActionTypes.DELETE_SCRIPT,
      payload: script.id
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-5 flex items-start gap-4 hover:shadow-md transition-shadow duration-300 group">
      {/* Script Number */}
      <div className="flex-shrink-0 w-7 h-7 bg-blue-50 text-primary rounded-full flex items-center justify-center text-sm font-semibold mt-1">
        {index + 1}
      </div>

      {/* Script Content */}
      <div className="flex-1">
        {isEditing ? (
          <textarea
            value={editText}
            onChange={handleTextChange}
            onBlur={handleTextBlur}
            onKeyDown={handleTextKeyDown}
            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
            rows={3}
            autoFocus
          />
        ) : (
          <p
            className="text-gray-700 leading-relaxed cursor-text p-2 rounded-md hover:bg-gray-50 transition-colors duration-200"
            onClick={() => setIsEditing(true)}
          >
            {script.text}
          </p>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <button
          onClick={handleRegenerate}
          className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors duration-200"
          title="重新生成"
        >
          <span className="material-icons text-lg">refresh</span>
        </button>
        <button
          onClick={handleDelete}
          className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-full transition-colors duration-200"
          title="删除"
        >
          <span className="material-icons text-lg">delete</span>
        </button>
      </div>
    </div>
  );
};

export default ScriptItem;
