import { useState, useEffect } from 'react';
import { useApp } from '../../context/AppContext.jsx';
import { useNavigation } from '../../hooks/useNavigation';
import { useApi } from '../../hooks/useApi.js';
import Button from '../common/Button';
import ScriptList from './ScriptList';

const ScriptGeneration = () => {
  const { state, dispatch, ActionTypes } = useApp();
  const { setCurrentTab } = useNavigation();
  const { generateShots } = useApi();
  const [isGeneratingShots, setIsGeneratingShots] = useState(false);

  const handleAddScript = () => {
    dispatch({ type: ActionTypes.ADD_SCRIPT, payload: '' });
  };

  const handleConfirmScript = async () => {
    if (state.scripts.length === 0) {
      alert('请先生成或添加脚本内容');
      return;
    }

    setIsGeneratingShots(true);
    try {
      // 调用API生成镜头
      await generateShots(state.scripts);
      // 导航到镜头编辑页面
      setCurrentTab('storyboard');
    } catch (error) {
      console.error('生成镜头失败:', error);
      alert('生成镜头失败，请重试');
    } finally {
      setIsGeneratingShots(false);
    }
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          AI脚本生成
        </h2>
        <p className="text-gray-600">
          AI已为您生成以下脚本，您可以对每一条进行编辑或重新生成
        </p>
      </div>

      {/* Script List */}
      <div className="mb-8">
        <ScriptList />
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center">
        <Button
          variant="secondary"
          onClick={handleAddScript}
          icon={<span className="material-icons text-lg">add</span>}
        >
          添加新条目
        </Button>
        
        <Button
          variant="primary"
          onClick={handleConfirmScript}
          disabled={isGeneratingShots || state.isLoading}
        >
          {isGeneratingShots ? '正在生成镜头...' : '确认脚本并生成镜头'}
        </Button>
      </div>
    </div>
  );
};

export default ScriptGeneration;
