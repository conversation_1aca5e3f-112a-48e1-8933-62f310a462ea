import { useApp } from '../../context/AppContext.jsx';
import { useNavigation } from '../../hooks/useNavigation';
import Button from '../common/Button';
import ScriptList from './ScriptList';

const ScriptGeneration = () => {
  const { dispatch, ActionTypes } = useApp();
  const { setCurrentTab } = useNavigation();

  const handleAddScript = () => {
    dispatch({ type: ActionTypes.ADD_SCRIPT, payload: '' });
  };

  const handleConfirmScript = () => {
    // Navigate to storyboard
    setCurrentTab('storyboard');
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          AI脚本生成
        </h2>
        <p className="text-gray-600">
          AI已为您生成以下脚本，您可以对每一条进行编辑或重新生成
        </p>
      </div>

      {/* Script List */}
      <div className="mb-8">
        <ScriptList />
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center">
        <Button
          variant="secondary"
          onClick={handleAddScript}
          icon={<span className="material-icons text-lg">add</span>}
        >
          添加新条目
        </Button>
        
        <Button
          variant="primary"
          onClick={handleConfirmScript}
        >
          确认脚本并生成镜头
        </Button>
      </div>
    </div>
  );
};

export default ScriptGeneration;
