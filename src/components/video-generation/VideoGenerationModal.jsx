import { useState } from 'react';
import Modal from '../common/Modal';
import Button from '../common/Button';

const VideoGenerationModal = ({ isOpen, onClose, shotData, onGenerate }) => {
  const [settings, setSettings] = useState({
    style: 'realistic',
    quality: 'high',
    duration: 10,
    frameRate: '30fps',
    aspectRatio: '16:9',
    motion: 'moderate',
    lighting: 'natural',
    customPrompt: ''
  });

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleGenerate = () => {
    if (shotData?.shotId) {
      onGenerate(shotData.shotId);
    }
    onClose();
  };

  const handleCancel = () => {
    setSettings({
      style: 'realistic',
      quality: 'high',
      duration: 10,
      frameRate: '30fps',
      aspectRatio: '16:9',
      motion: 'moderate',
      lighting: 'natural',
      customPrompt: ''
    });
    onClose();
  };

  if (!shotData) return null;

  const options = {
    style: [
      { value: 'realistic', label: '写实风格', description: '真实自然的视觉效果' },
      { value: 'animated', label: '动画风格', description: '卡通动画效果' },
      { value: 'cinematic', label: '电影风格', description: '电影级视觉质感' },
      { value: 'documentary', label: '纪录片', description: '纪实拍摄风格' }
    ],
    quality: [
      { value: 'standard', label: '标准质量', description: '720p, 适合快速预览' },
      { value: 'high', label: '高清质量', description: '1080p, 推荐选择' },
      { value: 'ultra', label: '超高清', description: '4K, 最佳质量' }
    ],
    motion: [
      { value: 'static', label: '静态', description: '最小运动效果' },
      { value: 'moderate', label: '适中', description: '自然运动效果' },
      { value: 'dynamic', label: '动态', description: '丰富运动效果' }
    ],
    lighting: [
      { value: 'natural', label: '自然光', description: '模拟自然光照' },
      { value: 'studio', label: '摄影棚', description: '专业摄影棚光照' },
      { value: 'dramatic', label: '戏剧性', description: '强烈对比光照' }
    ]
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleCancel}
      title="自定义视频设置"
      size="md"
      className="max-h-[90vh] overflow-y-auto"
      footer={
        <>
          <Button variant="secondary" onClick={handleCancel}>
            取消
          </Button>
          <Button variant="primary" onClick={handleGenerate}>
            生成视频
          </Button>
        </>
      }
    >
      <div className="space-y-4">
        {/* Shot Preview */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            当前镜头
          </label>
          <div className="flex gap-3 p-3 bg-gray-50 rounded-lg">
            <img
              src={shotData.shot?.imageUrl}
              alt="镜头预览"
              className="w-16 h-10 object-cover rounded flex-shrink-0"
            />
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-700 leading-relaxed line-clamp-2">
                {shotData.shot?.scriptText}
              </p>
            </div>
          </div>
        </div>

        {/* Video Settings - Compact Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(options).map(([key, values]) => (
            <div key={key}>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {key === 'style' && '视频风格'}
                {key === 'quality' && '视频质量'}
                {key === 'motion' && '运动效果'}
                {key === 'lighting' && '光照效果'}
              </label>
              <div className="space-y-1">
                {values.map((option) => (
                  <label
                    key={option.value}
                    className={`
                      flex items-center p-2 border rounded cursor-pointer transition-all duration-200
                      ${settings[key] === option.value
                        ? 'border-primary bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                      }
                    `}
                  >
                    <input
                      type="radio"
                      name={key}
                      value={option.value}
                      checked={settings[key] === option.value}
                      onChange={() => handleSettingChange(key, option.value)}
                      className="w-4 h-4 text-primary focus:ring-primary border-gray-300 mr-2"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900">
                        {option.label}
                      </div>
                      <div className="text-xs text-gray-500 truncate">
                        {option.description}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Duration and Technical Settings */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              视频时长 (秒)
            </label>
            <input
              type="range"
              min="5"
              max="30"
              value={settings.duration}
              onChange={(e) => handleSettingChange('duration', parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>5秒</span>
              <span className="font-medium">{settings.duration}秒</span>
              <span>30秒</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              帧率
            </label>
            <select
              value={settings.frameRate}
              onChange={(e) => handleSettingChange('frameRate', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="24fps">24fps (电影)</option>
              <option value="30fps">30fps (标准)</option>
              <option value="60fps">60fps (流畅)</option>
            </select>
          </div>
        </div>

        {/* Custom Prompt */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            自定义要求 (可选)
          </label>
          <textarea
            value={settings.customPrompt}
            onChange={(e) => handleSettingChange('customPrompt', e.target.value)}
            placeholder="例如：增加特定的视觉效果、调整色调、添加特殊元素等..."
            rows={2}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
          />
          <p className="text-xs text-gray-500 mt-1">
            描述您对这段视频的特殊要求
          </p>
        </div>

        {/* Preview Settings - Compact */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="text-sm font-medium text-gray-700 mb-2">当前设置预览</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-600">风格:</span>
              <span className="text-gray-900 font-medium">
                {options.style.find(s => s.value === settings.style)?.label}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">质量:</span>
              <span className="text-gray-900 font-medium">
                {options.quality.find(q => q.value === settings.quality)?.label}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">时长:</span>
              <span className="text-gray-900 font-medium">{settings.duration}秒</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">帧率:</span>
              <span className="text-gray-900 font-medium">{settings.frameRate}</span>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default VideoGenerationModal;
