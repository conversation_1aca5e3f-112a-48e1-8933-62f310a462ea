import { useState } from 'react';
import Button from '../common/Button';
import VideoPlayerModal from '../common/VideoPlayerModal.jsx';

const VideoCard = ({ 
  shot, 
  index, 
  videoSettings, 
  isGenerating, 
  onStyleSelect, 
  onGenerate, 
  onCustomize 
}) => {
  const [showStyleOptions, setShowStyleOptions] = useState(false);
  const [showVideoPlayer, setShowVideoPlayer] = useState(false);

  const videoStyles = [
    {
      id: 'realistic',
      name: '写实风格',
      description: '真实自然',
      icon: '🎬'
    },
    {
      id: 'animated',
      name: '动画风格',
      description: '卡通动画',
      icon: '🎨'
    },
    {
      id: 'cinematic',
      name: '电影风格',
      description: '电影质感',
      icon: '🎭'
    },
    {
      id: 'documentary',
      name: '纪录片',
      description: '纪实风格',
      icon: '📹'
    }
  ];

  const selectedStyle = videoStyles.find(s => s.id === videoSettings.style) || videoStyles[0];

  const handleStyleSelect = (style) => {
    onStyleSelect(style);
    setShowStyleOptions(false);
  };

  const handlePlayVideo = () => {
    setShowVideoPlayer(true);
  };

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-lg transition-all duration-300 group">
      {/* Mobile: Vertical Layout, Desktop: Horizontal Layout */}
      <div className="flex flex-col md:flex-row">
        {/* Left: Video Preview (45% on desktop) */}
        <div className="relative bg-gray-50 h-[220px] md:h-[200px] md:w-[45%] flex-shrink-0 p-2">
          {videoSettings.isGenerated ? (
            <video
              poster={videoSettings.thumbnailUrl || shot.imageUrl}
              className="w-full h-full object-contain rounded-md"
              muted
              loop
            >
              <source src={videoSettings.videoUrl} type="video/mp4" />
            </video>
          ) : (
            <img
              src={shot.imageUrl}
              alt={`镜头 ${index + 1}`}
              className="w-full h-full object-contain rounded-md"
            />
          )}

          {/* Shot Number Badge */}
          <div className="absolute top-1 left-1 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-semibold shadow-lg">
            {index + 1}
          </div>

          {/* Video Status Badge */}
          <div className="absolute top-1 right-1">
            {videoSettings.isGenerated ? (
              <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs flex items-center gap-1 shadow-lg">
                <span className="material-icons text-sm">check_circle</span>
                已生成
              </div>
            ) : isGenerating ? (
              <div className="bg-blue-500 text-white px-2 py-1 rounded-full text-xs flex items-center gap-1 shadow-lg">
                <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                生成中
              </div>
            ) : (
              <div className="bg-gray-500 text-white px-2 py-1 rounded-full text-xs shadow-lg">
                待生成
              </div>
            )}
          </div>

          {/* Play Button Overlay (when generated) */}
          {videoSettings.isGenerated && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <button
                onClick={handlePlayVideo}
                className="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all duration-200"
              >
                <span className="material-icons text-2xl text-gray-700 ml-0.5">play_arrow</span>
              </button>
            </div>
          )}

          {/* Generation Progress (when generating) */}
          {isGenerating && (
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
              <div className="text-white text-xs mb-1">AI正在生成视频...</div>
              <div className="w-full bg-white/20 rounded-full h-1">
                <div className="bg-white h-1 rounded-full animate-pulse" style={{ width: '60%' }}></div>
              </div>
            </div>
          )}
        </div>

        {/* Right: Content (55% on desktop) */}
        <div className="p-4 md:p-6 flex-1 flex flex-col">
          {/* Header */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-900 mb-2">
              镜头 {index + 1}
            </h3>
            {/* Script Text */}
            <p className="text-sm text-gray-600 leading-relaxed line-clamp-3">
              {shot.scriptText}
            </p>
          </div>

          {/* Video Style Selection */}
          <div className="mb-4">
            <label className="block text-xs font-medium text-gray-700 mb-2">
              视频风格
            </label>
            <div className="relative">
              <button
                onClick={() => setShowStyleOptions(!showStyleOptions)}
                className="w-full flex items-center justify-between p-2 border border-gray-200 rounded-md hover:border-gray-300 transition-colors duration-200"
              >
                <div className="flex items-center gap-2">
                  <span className="text-lg">{selectedStyle.icon}</span>
                  <div className="text-left">
                    <div className="text-sm font-medium text-gray-900">
                      {selectedStyle.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {selectedStyle.description}
                    </div>
                  </div>
                </div>
                <span className="material-icons text-gray-400">
                  {showStyleOptions ? 'expand_less' : 'expand_more'}
                </span>
              </button>

              {/* Style Options Dropdown */}
              {showStyleOptions && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                  {videoStyles.map((style) => (
                    <button
                      key={style.id}
                      onClick={() => handleStyleSelect(style.id)}
                      className={`
                        w-full flex items-center gap-2 p-2 text-left hover:bg-gray-50 transition-colors duration-200
                        ${style.id === selectedStyle.id ? 'bg-blue-50 text-primary' : 'text-gray-700'}
                      `}
                    >
                      <span className="text-lg">{style.icon}</span>
                      <div>
                        <div className="text-sm font-medium">
                          {style.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {style.description}
                        </div>
                      </div>
                      {style.id === selectedStyle.id && (
                        <span className="material-icons text-primary ml-auto">check</span>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Video Info (when generated) */}
          {videoSettings.isGenerated && (
            <div className="bg-gray-50 rounded-lg p-3 mb-4">
              <div className="flex items-center justify-between text-xs text-gray-600">
                <span>时长: {videoSettings.duration}秒</span>
                <span>质量: {videoSettings.quality === 'high' ? '高清' : '标清'}</span>
              </div>
              <div className="mt-2 flex items-center gap-2">
                <div className="flex-1 bg-gray-200 rounded-full h-1">
                  <div className="bg-primary h-1 rounded-full" style={{ width: '0%' }}></div>
                </div>
                <span className="text-xs text-gray-500">0:00</span>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 mt-auto">
            {videoSettings.isGenerated ? (
              <>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={onGenerate}
                  className="flex-1"
                >
                  重新生成
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={onCustomize}
                  className="px-3"
                >
                  <span className="material-icons text-sm">tune</span>
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={onGenerate}
                  disabled={isGenerating}
                  className="flex-1"
                >
                  {isGenerating ? (
                    <>
                      <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin mr-1"></div>
                      生成中
                    </>
                  ) : (
                    '生成视频'
                  )}
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={onCustomize}
                  className="px-3"
                >
                  <span className="material-icons text-sm">tune</span>
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Video Player Modal */}
      <VideoPlayerModal
        isOpen={showVideoPlayer}
        onClose={() => setShowVideoPlayer(false)}
        videoUrl={videoSettings.videoUrl || `https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4`}
        title={`镜头 ${index + 1} - ${selectedStyle.name}`}
        description={shot.scriptText}
        thumbnailUrl={videoSettings.thumbnailUrl || shot.imageUrl}
      />
    </div>
  );
};

export default VideoCard;
