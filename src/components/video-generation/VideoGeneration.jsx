import { useState, useEffect } from 'react';
import { useApp } from '../../context/AppContext.jsx';
import { useNavigation } from '../../hooks/useNavigation';
import Button from '../common/Button';
import VideoCard from './VideoCard';
import VideoGenerationModal from './VideoGenerationModal';
import { useModal } from '../../hooks/useModal';

const VideoGeneration = () => {
  const { state, dispatch, ActionTypes } = useApp();
  const { setCurrentTab } = useNavigation();
  const [shotVideos, setShotVideos] = useState({});
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatingShots, setGeneratingShots] = useState(new Set());
  const { isOpen, modalData, openModal, closeModal } = useModal();

  // 初始化每个镜头的视频设置
  useEffect(() => {
    if (state.shots.length > 0) {
      const initialVideos = {};
      state.shots.forEach(shot => {
        if (!shotVideos[shot.id]) {
          initialVideos[shot.id] = {
            style: 'realistic',
            quality: 'high',
            duration: 10,
            isGenerated: false,
            videoUrl: null,
            thumbnailUrl: null
          };
        }
      });
      setShotVideos(prev => ({ ...prev, ...initialVideos }));
    }
  }, [state.shots]);

  const handleStyleSelect = (shotId, style) => {
    setShotVideos(prev => ({
      ...prev,
      [shotId]: {
        ...prev[shotId],
        style,
        isGenerated: false,
        videoUrl: null
      }
    }));
  };

  const handleGenerateVideo = async (shotId) => {
    setGeneratingShots(prev => new Set([...prev, shotId]));
    
    // 模拟视频生成过程
    setTimeout(() => {
      setShotVideos(prev => ({
        ...prev,
        [shotId]: {
          ...prev[shotId],
          isGenerated: true,
          videoUrl: `https://videos.pexels.com/video-files/853875/853875-hd_1280_720_25fps.mp4`,
          thumbnailUrl: `https://picsum.photos/seed/video-${shotId}/400/225`
        }
      }));
      setGeneratingShots(prev => {
        const newSet = new Set(prev);
        newSet.delete(shotId);
        return newSet;
      });
    }, 3000 + Math.random() * 3000);
  };

  const handleGenerateAll = async () => {
    setIsGenerating(true);
    
    // 为所有镜头生成视频
    const promises = state.shots.map(shot => 
      new Promise(resolve => {
        setTimeout(() => {
          setShotVideos(prev => ({
            ...prev,
            [shot.id]: {
              ...prev[shot.id],
              isGenerated: true,
              videoUrl: `https://videos.pexels.com/video-files/853875/853875-hd_1280_720_25fps.mp4`,
              thumbnailUrl: `https://picsum.photos/seed/video-${shot.id}/400/225`
            }
          }));
          resolve();
        }, 2000 + Math.random() * 4000);
      })
    );

    await Promise.all(promises);
    dispatch({ type: ActionTypes.GENERATE_VIDEO });
    setIsGenerating(false);
  };

  const handleContinue = () => {
    setCurrentTab('avatar');
  };

  const allVideosGenerated = state.shots.every(shot => 
    shotVideos[shot.id]?.isGenerated
  );

  if (state.shots.length === 0) {
    return (
      <div>
        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            生成视频
          </h2>
          <p className="text-gray-600">
            为每个镜头选择合适的视频风格，AI将生成高质量的视频片段
          </p>
        </div>
        
        <div className="text-center py-12">
          <span className="material-icons text-6xl text-gray-300 mb-4 block">
            videocam
          </span>
          <p className="text-gray-500 text-lg">
            请先在&quot;镜头编辑&quot;页面创建镜头，才能生成对应的视频。
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          生成视频
        </h2>
        <p className="text-gray-600">
          为每个镜头选择合适的视频风格，AI将生成高质量的视频片段
        </p>
      </div>

      {/* Video Cards Grid */}
      <div className="grid grid-cols-1 gap-6 mb-8">
        {state.shots.map((shot, index) => (
          <VideoCard
            key={shot.id}
            shot={shot}
            index={index}
            videoSettings={shotVideos[shot.id] || {}}
            isGenerating={generatingShots.has(shot.id)}
            onStyleSelect={(style) => handleStyleSelect(shot.id, style)}
            onGenerate={() => handleGenerateVideo(shot.id)}
            onCustomize={() => openModal({ shotId: shot.id, shot })}
          />
        ))}
      </div>

      {/* Batch Actions */}
      <div className="bg-white rounded-lg p-6 shadow-sm mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              批量操作
            </h3>
            <p className="text-gray-600">
              为所有镜头快速生成视频，或统一设置视频风格
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="secondary"
              onClick={() => {
                // 统一设置所有镜头为写实风格
                state.shots.forEach(shot => {
                  handleStyleSelect(shot.id, 'realistic');
                });
              }}
            >
              统一设置
            </Button>
            <Button
              variant="primary"
              onClick={handleGenerateAll}
              disabled={isGenerating}
            >
              {isGenerating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  正在生成全部视频...
                </>
              ) : (
                '生成全部视频'
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      {allVideosGenerated && (
        <div className="flex justify-end">
          <Button
            variant="primary"
            onClick={handleContinue}
          >
            确认视频并添加数字人
          </Button>
        </div>
      )}

      {/* Video Generation Modal */}
      <VideoGenerationModal
        isOpen={isOpen}
        onClose={closeModal}
        shotData={modalData}
        onGenerate={handleGenerateVideo}
      />
    </div>
  );
};

export default VideoGeneration;
