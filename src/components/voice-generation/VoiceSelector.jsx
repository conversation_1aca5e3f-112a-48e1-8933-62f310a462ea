import React from 'react';
import { useApp } from '../../context/AppContext.jsx';

const VoiceSelector = () => {
  const { state, dispatch, ActionTypes } = useApp();

  const voices = [
    {
      id: 'professional-female',
      name: '专业女声',
      description: '清晰自然的女性声音，适合商务和教育内容',
      characteristics: ['清晰', '专业', '自然']
    },
    {
      id: 'energetic-male',
      name: '活力男声',
      description: '充满能量的男性声音，适合营销和宣传内容',
      characteristics: ['活力', '热情', '感染力']
    },
    {
      id: 'gentle-child',
      name: '温柔童声',
      description: '天真可爱的儿童声音，适合儿童和家庭内容',
      characteristics: ['可爱', '温柔', '亲和']
    },
    {
      id: 'news-anchor',
      name: '新闻主播',
      description: '权威正式的播报风格，适合新闻和正式场合',
      characteristics: ['权威', '正式', '稳重']
    }
  ];

  const handleVoiceSelect = (voiceId) => {
    dispatch({ type: ActionTypes.SET_SELECTED_VOICE, payload: voiceId });
  };

  const handlePlaySample = (e, voiceId) => {
    e.stopPropagation();
    console.log(`Playing voice sample for: ${voiceId}`);
    // 实现语音试听逻辑
  };

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">选择配音类型</h3>
      
      <div className="space-y-4">
        {voices.map((voice) => {
          const isSelected = state.selectedVoice === voice.id;
          
          return (
            <div
              key={voice.id}
              onClick={() => handleVoiceSelect(voice.id)}
              className={`
                p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 hover:shadow-md
                ${isSelected 
                  ? 'border-primary bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
                }
              `}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`
                      w-10 h-10 rounded-full flex items-center justify-center
                      ${isSelected ? 'bg-blue-100' : 'bg-gray-100'}
                    `}>
                      <span className={`
                        material-icons text-xl
                        ${isSelected ? 'text-primary' : 'text-gray-500'}
                      `}>
                        record_voice_over
                      </span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {voice.name}
                      </h4>
                      <div className="flex gap-1 mt-1">
                        {voice.characteristics.map((char, index) => (
                          <span
                            key={index}
                            className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                          >
                            {char}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    {voice.description}
                  </p>
                </div>
                
                <button
                  onClick={(e) => handlePlaySample(e, voice.id)}
                  className="ml-4 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-full transition-colors duration-200 flex items-center gap-1"
                >
                  <span className="material-icons text-sm">play_arrow</span>
                  试听
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default VoiceSelector;
