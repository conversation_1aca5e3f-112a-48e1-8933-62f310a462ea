import React from 'react';
import { useApp } from '../../context/AppContext.jsx';

const VoicePreview = ({ isGenerated, isGenerating }) => {
  const { state } = useApp();

  if (isGenerating) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          配音生成中
        </h3>
        <div className="flex flex-col items-center justify-center py-12">
          <div className="w-16 h-16 border-4 border-gray-200 border-t-primary rounded-full animate-spin mb-4"></div>
          <p className="text-gray-600 text-center">
            AI正在为您的脚本生成高质量配音，请稍候...
          </p>
          <div className="mt-4 w-full bg-gray-200 rounded-full h-2">
            <div className="bg-primary h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
          </div>
        </div>
      </div>
    );
  }

  if (!isGenerated) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          配音预览
        </h3>
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <span className="material-icons text-6xl text-gray-300 mb-4">
            mic_none
          </span>
          <p className="text-gray-500">
            选择配音类型后，点击"开始生成配音"来创建您的专业配音
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        配音预览
      </h3>
      
      {/* Script List with Audio */}
      <div className="space-y-4 mb-6">
        {state.scripts.map((script, index) => (
          <div key={script.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-start gap-3 mb-3">
              <div className="flex-shrink-0 w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-semibold">
                {index + 1}
              </div>
              <p className="text-sm text-gray-700 leading-relaxed">
                {script.text}
              </p>
            </div>
            
            {/* Audio Player */}
            <div className="bg-gray-50 rounded-lg p-3 flex items-center gap-3">
              <button className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors">
                <span className="material-icons text-sm">play_arrow</span>
              </button>
              <div className="flex-1 bg-gray-200 rounded-full h-2 relative">
                <div className="bg-primary h-2 rounded-full" style={{ width: '0%' }}></div>
              </div>
              <span className="text-xs text-gray-500">0:00 / 0:15</span>
            </div>
          </div>
        ))}
      </div>

      {/* Overall Controls */}
      <div className="border-t border-gray-200 pt-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <button className="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors">
              <span className="material-icons">play_arrow</span>
            </button>
            <button className="w-10 h-10 bg-gray-100 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors">
              <span className="material-icons">pause</span>
            </button>
            <button className="w-10 h-10 bg-gray-100 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors">
              <span className="material-icons">stop</span>
            </button>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="material-icons text-gray-400">volume_up</span>
            <div className="w-20 bg-gray-200 rounded-full h-2">
              <div className="bg-primary h-2 rounded-full" style={{ width: '70%' }}></div>
            </div>
          </div>
        </div>
        
        <div className="mt-3 text-center">
          <p className="text-sm text-gray-600">
            总时长: 2分30秒 | 配音质量: 高清
          </p>
        </div>
      </div>
    </div>
  );
};

export default VoicePreview;
