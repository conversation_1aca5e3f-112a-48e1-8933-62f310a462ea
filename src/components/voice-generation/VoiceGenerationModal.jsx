import { useState } from 'react';
import Modal from '../common/Modal';
import Button from '../common/Button';

const VoiceGenerationModal = ({ isOpen, onClose, shotData, onGenerate }) => {
  const [settings, setSettings] = useState({
    speed: 'normal',
    pitch: 'normal',
    emotion: 'neutral',
    emphasis: 'none',
    customPrompt: ''
  });

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleGenerate = () => {
    if (shotData?.shotId) {
      onGenerate(shotData.shotId);
    }
    onClose();
  };

  const handleCancel = () => {
    setSettings({
      speed: 'normal',
      pitch: 'normal',
      emotion: 'neutral',
      emphasis: 'none',
      customPrompt: ''
    });
    onClose();
  };

  if (!shotData) return null;

  const options = {
    speed: [
      { value: 'slow', label: '慢速 (0.8x)', description: '适合教学内容' },
      { value: 'normal', label: '正常 (1.0x)', description: '标准语速' },
      { value: 'fast', label: '快速 (1.2x)', description: '适合快节奏内容' }
    ],
    pitch: [
      { value: 'low', label: '低音调', description: '更加沉稳' },
      { value: 'normal', label: '正常音调', description: '自然音调' },
      { value: 'high', label: '高音调', description: '更加活泼' }
    ],
    emotion: [
      { value: 'neutral', label: '中性', description: '平稳自然' },
      { value: 'happy', label: '愉悦', description: '积极向上' },
      { value: 'serious', label: '严肃', description: '正式庄重' },
      { value: 'gentle', label: '温和', description: '亲切友好' }
    ],
    emphasis: [
      { value: 'none', label: '无重点', description: '平均语调' },
      { value: 'keywords', label: '关键词', description: '突出重要词汇' },
      { value: 'sentences', label: '句子', description: '突出重要句子' }
    ]
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleCancel}
      title="自定义配音设置"
      size="md"
      className="max-h-[90vh] overflow-y-auto"
      footer={
        <>
          <Button variant="secondary" onClick={handleCancel}>
            取消
          </Button>
          <Button variant="primary" onClick={handleGenerate}>
            生成配音
          </Button>
        </>
      }
    >
      <div className="space-y-4">
        {/* Shot Preview */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            当前镜头
          </label>
          <div className="flex gap-3 p-3 bg-gray-50 rounded-lg">
            <img
              src={shotData.shot?.imageUrl}
              alt="镜头预览"
              className="w-16 h-10 object-cover rounded flex-shrink-0"
            />
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-700 leading-relaxed line-clamp-2">
                {shotData.shot?.scriptText}
              </p>
            </div>
          </div>
        </div>

        {/* Voice Settings - Compact Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(options).map(([key, values]) => (
            <div key={key}>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {key === 'speed' && '语速设置'}
                {key === 'pitch' && '音调设置'}
                {key === 'emotion' && '情感色彩'}
                {key === 'emphasis' && '重点强调'}
              </label>
              <div className="space-y-1">
                {values.map((option) => (
                  <label
                    key={option.value}
                    className={`
                      flex items-center p-2 border rounded cursor-pointer transition-all duration-200
                      ${settings[key] === option.value
                        ? 'border-primary bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                      }
                    `}
                  >
                    <input
                      type="radio"
                      name={key}
                      value={option.value}
                      checked={settings[key] === option.value}
                      onChange={() => handleSettingChange(key, option.value)}
                      className="w-4 h-4 text-primary focus:ring-primary border-gray-300 mr-2"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900">
                        {option.label}
                      </div>
                      <div className="text-xs text-gray-500 truncate">
                        {option.description}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Custom Prompt */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            自定义要求 (可选)
          </label>
          <textarea
            value={settings.customPrompt}
            onChange={(e) => handleSettingChange('customPrompt', e.target.value)}
            placeholder="例如：在某些词语上加重语气、增加停顿、调整语调变化等..."
            rows={2}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
          />
          <p className="text-xs text-gray-500 mt-1">
            描述您对这段配音的特殊要求
          </p>
        </div>

        {/* Preview Settings - Compact */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="text-sm font-medium text-gray-700 mb-2">当前设置预览</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-600">语速:</span>
              <span className="text-gray-900 font-medium">
                {options.speed.find(s => s.value === settings.speed)?.label}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">音调:</span>
              <span className="text-gray-900 font-medium">
                {options.pitch.find(p => p.value === settings.pitch)?.label}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">情感:</span>
              <span className="text-gray-900 font-medium">
                {options.emotion.find(e => e.value === settings.emotion)?.label}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">强调:</span>
              <span className="text-gray-900 font-medium">
                {options.emphasis.find(e => e.value === settings.emphasis)?.label}
              </span>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default VoiceGenerationModal;
