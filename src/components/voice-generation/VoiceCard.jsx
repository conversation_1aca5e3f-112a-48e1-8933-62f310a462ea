import { useState } from 'react';
import Button from '../common/Button';
import AudioPlayerModal from '../common/AudioPlayerModal.jsx';

const VoiceCard = ({ 
  shot, 
  index, 
  voiceSettings, 
  isGenerating, 
  onVoiceSelect, 
  onGenerate, 
  onCustomize 
}) => {
  const [showVoiceOptions, setShowVoiceOptions] = useState(false);
  const [showAudioPlayer, setShowAudioPlayer] = useState(false);

  const voiceTypes = [
    {
      id: 'professional-female',
      name: '专业女声',
      description: '清晰自然',
      icon: '👩‍💼'
    },
    {
      id: 'energetic-male',
      name: '活力男声',
      description: '充满能量',
      icon: '👨‍💼'
    },
    {
      id: 'gentle-child',
      name: '温柔童声',
      description: '天真可爱',
      icon: '👶'
    },
    {
      id: 'news-anchor',
      name: '新闻主播',
      description: '权威正式',
      icon: '📺'
    }
  ];

  const selectedVoice = voiceTypes.find(v => v.id === voiceSettings.voiceType) || voiceTypes[0];

  const handleVoiceTypeSelect = (voiceType) => {
    onVoiceSelect(voiceType);
    setShowVoiceOptions(false);
  };

  const handlePlayAudio = () => {
    setShowAudioPlayer(true);
  };

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-lg transition-all duration-300 group">
      {/* Mobile: Vertical Layout, Desktop: Horizontal Layout */}
      <div className="flex flex-col md:flex-row">
        {/* Left: Shot Image (45% on desktop) */}
        <div className="relative bg-gray-50 h-[220px] md:h-[200px] md:w-[45%] flex-shrink-0 p-2">
          <img
            src={shot.imageUrl}
            alt={`镜头 ${index + 1}`}
            className="w-full h-full object-contain rounded-md"
          />

          {/* Shot Number Badge */}
          <div className="absolute top-1 left-1 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-semibold shadow-lg">
            {index + 1}
          </div>

          {/* Voice Status Badge */}
          <div className="absolute top-1 right-1">
            {voiceSettings.isGenerated ? (
              <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs flex items-center gap-1 shadow-lg">
                <span className="material-icons text-sm">check_circle</span>
                已生成
              </div>
            ) : isGenerating ? (
              <div className="bg-blue-500 text-white px-2 py-1 rounded-full text-xs flex items-center gap-1 shadow-lg">
                <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                生成中
              </div>
            ) : (
              <div className="bg-gray-500 text-white px-2 py-1 rounded-full text-xs shadow-lg">
                待生成
              </div>
            )}
          </div>

          {/* Audio Player Overlay (when generated) */}
          {voiceSettings.isGenerated && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <button
                onClick={handlePlayAudio}
                className="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all duration-200"
              >
                <span className="material-icons text-2xl text-gray-700 ml-0.5">play_arrow</span>
              </button>
            </div>
          )}
        </div>

        {/* Right: Content (55% on desktop) */}
        <div className="p-4 md:p-6 flex-1 flex flex-col">
          {/* Header */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-900 mb-2">
              镜头 {index + 1}
            </h3>
            {/* Script Text */}
            <p className="text-sm text-gray-600 leading-relaxed line-clamp-3">
              {shot.scriptText}
            </p>
          </div>

          {/* Voice Selection */}
          <div className="mb-4">
            <label className="block text-xs font-medium text-gray-700 mb-2">
              配音类型
            </label>
            <div className="relative">
              <button
                onClick={() => setShowVoiceOptions(!showVoiceOptions)}
                className="w-full flex items-center justify-between p-2 border border-gray-200 rounded-md hover:border-gray-300 transition-colors duration-200"
              >
                <div className="flex items-center gap-2">
                  <span className="text-lg">{selectedVoice.icon}</span>
                  <div className="text-left">
                    <div className="text-sm font-medium text-gray-900">
                      {selectedVoice.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {selectedVoice.description}
                    </div>
                  </div>
                </div>
                <span className="material-icons text-gray-400">
                  {showVoiceOptions ? 'expand_less' : 'expand_more'}
                </span>
              </button>

              {/* Voice Options Dropdown */}
              {showVoiceOptions && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                  {voiceTypes.map((voice) => (
                    <button
                      key={voice.id}
                      onClick={() => handleVoiceTypeSelect(voice.id)}
                      className={`
                        w-full flex items-center gap-2 p-2 text-left hover:bg-gray-50 transition-colors duration-200
                        ${voice.id === selectedVoice.id ? 'bg-blue-50 text-primary' : 'text-gray-700'}
                      `}
                    >
                      <span className="text-lg">{voice.icon}</span>
                      <div>
                        <div className="text-sm font-medium">
                          {voice.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {voice.description}
                        </div>
                      </div>
                      {voice.id === selectedVoice.id && (
                        <span className="material-icons text-primary ml-auto">check</span>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Audio Info (when generated) */}
          {voiceSettings.isGenerated && (
            <div className="bg-gray-50 rounded-lg p-3 mb-4">
              <div className="flex items-center justify-between text-xs text-gray-600">
                <span>时长: {Math.round(voiceSettings.duration)}秒</span>
                <span>质量: 高清</span>
              </div>
              <div className="mt-2 bg-gray-200 rounded-full h-1">
                <div className="bg-primary h-1 rounded-full" style={{ width: '0%' }}></div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 mt-auto">
            {voiceSettings.isGenerated ? (
              <>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={onGenerate}
                  className="flex-1"
                >
                  重新生成
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={onCustomize}
                  className="px-3"
                >
                  <span className="material-icons text-sm">tune</span>
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={onGenerate}
                  disabled={isGenerating}
                  className="flex-1"
                >
                  {isGenerating ? (
                    <>
                      <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin mr-1"></div>
                      生成中
                    </>
                  ) : (
                    '生成配音'
                  )}
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={onCustomize}
                  className="px-3"
                >
                  <span className="material-icons text-sm">tune</span>
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Audio Player Modal */}
      <AudioPlayerModal
        isOpen={showAudioPlayer}
        onClose={() => setShowAudioPlayer(false)}
        audioUrl={voiceSettings.audioUrl || `https://www.soundjay.com/misc/sounds/bell-ringing-05.wav`}
        title={`镜头 ${index + 1} - ${selectedVoice.name}`}
        description={shot.scriptText}
      />
    </div>
  );
};

export default VoiceCard;
