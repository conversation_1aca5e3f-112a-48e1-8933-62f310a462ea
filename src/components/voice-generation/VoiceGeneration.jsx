import { useState, useEffect } from 'react';
import { useApp } from '../../context/AppContext.jsx';
import { useNavigation } from '../../hooks/useNavigation';
import Button from '../common/Button';
import VoiceCard from './VoiceCard.jsx';
import VoiceGenerationModal from './VoiceGenerationModal.jsx';
import { useModal } from '../../hooks/useModal';

const VoiceGeneration = () => {
  const { state, dispatch, ActionTypes } = useApp();
  const { setCurrentTab } = useNavigation();
  const [shotVoices, setShotVoices] = useState({});
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatingShots, setGeneratingShots] = useState(new Set());
  const { isOpen, modalData, openModal, closeModal } = useModal();

  // 初始化每个镜头的配音设置
  useEffect(() => {
    if (state.shots.length > 0) {
      const initialVoices = {};
      state.shots.forEach(shot => {
        if (!shotVoices[shot.id]) {
          initialVoices[shot.id] = {
            voiceType: 'professional-female',
            isGenerated: false,
            audioUrl: null,
            duration: 0
          };
        }
      });
      setShotVoices(prev => ({ ...prev, ...initialVoices }));
    }
  }, [state.shots]);

  const handleVoiceSelect = (shotId, voiceType) => {
    setShotVoices(prev => ({
      ...prev,
      [shotId]: {
        ...prev[shotId],
        voiceType,
        isGenerated: false,
        audioUrl: null
      }
    }));
  };

  const handleGenerateVoice = async (shotId) => {
    setGeneratingShots(prev => new Set([...prev, shotId]));

    // 模拟配音生成过程
    setTimeout(() => {
      setShotVoices(prev => ({
        ...prev,
        [shotId]: {
          ...prev[shotId],
          isGenerated: true,
          audioUrl: `https://example.com/audio/${shotId}.mp3`,
          duration: 10 + Math.random() * 15 // 随机时长
        }
      }));
      setGeneratingShots(prev => {
        const newSet = new Set(prev);
        newSet.delete(shotId);
        return newSet;
      });
    }, 2000 + Math.random() * 2000);
  };

  const handleGenerateAll = async () => {
    setIsGenerating(true);

    // 为所有镜头生成配音
    const promises = state.shots.map(shot =>
      new Promise(resolve => {
        setTimeout(() => {
          setShotVoices(prev => ({
            ...prev,
            [shot.id]: {
              ...prev[shot.id],
              isGenerated: true,
              audioUrl: `https://example.com/audio/${shot.id}.mp3`,
              duration: 10 + Math.random() * 15
            }
          }));
          resolve();
        }, 1000 + Math.random() * 3000);
      })
    );

    await Promise.all(promises);
    dispatch({ type: ActionTypes.GENERATE_VOICE });
    setIsGenerating(false);
  };

  const handleContinue = () => {
    setCurrentTab('video');
  };

  const allVoicesGenerated = state.shots.every(shot =>
    shotVoices[shot.id]?.isGenerated
  );

  if (state.shots.length === 0) {
    return (
      <div>
        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            生成配音
          </h2>
          <p className="text-gray-600">
            为每个镜头选择合适的配音类型，AI将生成专业的配音效果
          </p>
        </div>

        <div className="text-center py-12">
          <span className="material-icons text-6xl text-gray-300 mb-4 block">
            mic_none
          </span>
          <p className="text-gray-500 text-lg">
            请先在&quot;镜头编辑&quot;页面创建镜头，才能生成对应的配音。
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          生成配音
        </h2>
        <p className="text-gray-600">
          为每个镜头选择合适的配音类型，AI将生成专业的配音效果
        </p>
      </div>

      {/* Voice Cards Grid */}
      <div className="grid grid-cols-1 gap-6 mb-8">
        {state.shots.map((shot, index) => (
          <VoiceCard
            key={shot.id}
            shot={shot}
            index={index}
            voiceSettings={shotVoices[shot.id] || {}}
            isGenerating={generatingShots.has(shot.id)}
            onVoiceSelect={(voiceType) => handleVoiceSelect(shot.id, voiceType)}
            onGenerate={() => handleGenerateVoice(shot.id)}
            onCustomize={() => openModal({ shotId: shot.id, shot })}
          />
        ))}
      </div>

      {/* Batch Actions */}
      <div className="bg-white rounded-lg p-6 shadow-sm mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              批量操作
            </h3>
            <p className="text-gray-600">
              为所有镜头快速生成配音，或统一设置配音类型
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="secondary"
              onClick={() => {
                // 统一设置所有镜头为专业女声
                state.shots.forEach(shot => {
                  handleVoiceSelect(shot.id, 'professional-female');
                });
              }}
            >
              统一设置
            </Button>
            <Button
              variant="primary"
              onClick={handleGenerateAll}
              disabled={isGenerating}
            >
              {isGenerating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  正在生成全部配音...
                </>
              ) : (
                '生成全部配音'
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      {allVoicesGenerated && (
        <div className="flex justify-end">
          <Button
            variant="primary"
            onClick={handleContinue}
          >
            确认配音并生成视频
          </Button>
        </div>
      )}

      {/* Voice Generation Modal */}
      <VoiceGenerationModal
        isOpen={isOpen}
        onClose={closeModal}
        shotData={modalData}
        onGenerate={handleGenerateVoice}
      />
    </div>
  );
};

export default VoiceGeneration;
