import { useState, useEffect } from 'react';
import { useApp } from '../../context/AppContext.jsx';
import { useNavigation } from '../../hooks/useNavigation';
import Button from '../common/Button';
import CompositionTimeline from './CompositionTimeline.jsx';
import VideoPreview from './VideoPreview.jsx';
import CompositionSettings from './CompositionSettings.jsx';


const VideoCompose = () => {
  const { state, dispatch, ActionTypes } = useApp();
  const { setCurrentTab } = useNavigation();
  const [isComposing, setIsComposing] = useState(false);
  const [compositionProgress, setCompositionProgress] = useState(0);

  // Auto-start composition when entering this tab
  useEffect(() => {
    if (!state.videoComposed && !isComposing) {
      handleStartComposition();
    }
  }, []);

  const handleStartComposition = async () => {
    setIsComposing(true);
    setCompositionProgress(0);
    
    // 模拟视频合成过程
    const interval = setInterval(() => {
      setCompositionProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsComposing(false);
          dispatch({ 
            type: ActionTypes.COMPOSE_VIDEO, 
            payload: 'https://videos.pexels.com/video-files/853875/853875-hd_1280_720_25fps.mp4'
          });
          return 100;
        }
        return prev + 10;
      });
    }, 500);
  };

  const handleRecompose = () => {
    handleStartComposition();
  };

  const handleContinue = () => {
    setCurrentTab('export');
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          视频合成
        </h2>
        <p className="text-gray-600">
          AI正在将您的脚本、镜头、配音和数字人合成为完整的视频
        </p>
      </div>

      {isComposing ? (
        /* Composition Progress View */
        <div className="space-y-8">
          {/* Progress Overview */}
          <div className="bg-white rounded-lg p-8 shadow-sm">
            <div className="text-center mb-8">
              <div className="w-20 h-20 border-4 border-gray-200 border-t-primary rounded-full animate-spin mx-auto mb-4"></div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                正在合成视频...
              </h3>
              <p className="text-gray-600">
                AI正在处理您的内容，请稍候片刻
              </p>
            </div>

            {/* Progress Bar */}
            <div className="max-w-lg mx-auto mb-8">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>合成进度</span>
                <span>{compositionProgress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-primary h-3 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${compositionProgress}%` }}
                ></div>
              </div>
            </div>

            {/* Composition Steps */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto">
              {[
                { step: '处理镜头', icon: 'photo_library', completed: compositionProgress > 25 },
                { step: '同步配音', icon: 'record_voice_over', completed: compositionProgress > 50 },
                { step: '添加数字人', icon: 'person', completed: compositionProgress > 75 },
                { step: '最终渲染', icon: 'movie_creation', completed: compositionProgress >= 100 }
              ].map((item, index) => (
                <div key={index} className="text-center">
                  <div className={`
                    w-12 h-12 rounded-full mx-auto mb-3 flex items-center justify-center transition-all duration-300
                    ${item.completed
                      ? 'bg-primary text-white shadow-lg'
                      : compositionProgress > (index * 25)
                        ? 'bg-blue-100 text-primary animate-pulse'
                        : 'bg-gray-200 text-gray-500'
                    }
                  `}>
                    <span className="material-icons text-lg">{item.icon}</span>
                  </div>
                  <p className={`text-sm font-medium ${
                    item.completed ? 'text-primary' :
                    compositionProgress > (index * 25) ? 'text-blue-600' : 'text-gray-500'
                  }`}>
                    {item.step}
                  </p>
                  {item.completed && (
                    <div className="mt-1">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                        <span className="material-icons text-xs mr-1">check</span>
                        完成
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Real-time Preview */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">实时预览</h3>
              <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <span className="material-icons text-4xl text-gray-400 mb-2 block animate-pulse">
                    movie_creation
                  </span>
                  <p className="text-gray-500 text-sm">合成中...</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">合成详情</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">总镜头数</span>
                  <span className="font-medium">{state.shots.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">预计时长</span>
                  <span className="font-medium">2分30秒</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">输出质量</span>
                  <span className="font-medium">1080p</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">预计完成</span>
                  <span className="font-medium">约{Math.ceil((100 - compositionProgress) / 10)}分钟</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        /* Composition Complete View */
        <div className="space-y-8">
          {/* Success Banner */}
          <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                <span className="material-icons text-white text-2xl">check_circle</span>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">视频合成完成！</h3>
                <p className="text-gray-600">您的视频已成功合成，可以进行预览和调整</p>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Video Preview - Larger */}
            <div className="lg:col-span-2">
              <VideoPreview
                videoUrl={state.composedVideoUrl}
                isComposed={state.videoComposed}
              />
            </div>

            {/* Right Sidebar */}
            <div className="lg:col-span-1 space-y-6">
              {/* Composition Settings */}
              <CompositionSettings onRecompose={handleRecompose} />
              {/* Quick Stats */}
            </div>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">视频信息</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">总时长</span>
                    <span className="font-medium">2:30</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">分辨率</span>
                    <span className="font-medium">1920×1080</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">帧率</span>
                    <span className="font-medium">30fps</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">文件大小</span>
                    <span className="font-medium">45.2MB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">格式</span>
                    <span className="font-medium">MP4</span>
                  </div>
                </div>
              </div>

              {/* Timeline */}
              <CompositionTimeline />
          </div>
        </div>
      )}

      {/* Action Buttons */}
      {state.videoComposed && !isComposing && (
        <div className="flex justify-between items-center mt-8 p-6 bg-white rounded-lg shadow-sm">
          <div>
            <h4 className="font-medium text-gray-900">准备导出视频</h4>
            <p className="text-sm text-gray-600">视频合成已完成，您可以进行最终导出</p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="secondary"
              onClick={handleRecompose}
              icon={<span className="material-icons text-sm">refresh</span>}
            >
              重新合成
            </Button>
            <Button
              variant="primary"
              onClick={handleContinue}
              icon={<span className="material-icons text-sm">file_download</span>}
            >
              导出视频
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoCompose;
