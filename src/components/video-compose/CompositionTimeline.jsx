import React from 'react';
import { useApp } from '../../context/AppContext.jsx';

const CompositionTimeline = () => {
  const { state } = useApp();

  const timelineItems = [
    { type: 'intro', duration: 5, color: 'bg-gradient-to-r from-blue-400 to-blue-600', title: '开场', icon: 'play_circle' },
    ...state.scripts.map((script, index) => ({
      type: 'scene',
      id: script.id,
      duration: 15 + Math.random() * 10,
      color: 'bg-gradient-to-r from-green-400 to-green-600',
      title: `镜头 ${index + 1}`,
      description: script.text.substring(0, 30) + '...',
      icon: 'movie'
    })),
    { type: 'outro', duration: 3, color: 'bg-gradient-to-r from-purple-400 to-purple-600', title: '结尾', icon: 'stop_circle' }
  ];

  const totalDuration = timelineItems.reduce((sum, item) => sum + item.duration, 0);

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <span className="material-icons text-primary">timeline</span>
        时间轴
      </h3>
      
      {/* Timeline Ruler */}
      <div className="mb-6">
        <div className="flex justify-between text-xs text-gray-500 mb-2">
          <span className="font-medium">0:00</span>
          <span className="font-medium">{Math.floor(totalDuration / 60)}:{(totalDuration % 60).toFixed(0).padStart(2, '0')}</span>
        </div>
        <div className="h-3 bg-gray-200 rounded-full overflow-hidden flex shadow-inner">
          {timelineItems.map((item, index) => (
            <div
              key={index}
              className={`${item.color} hover:opacity-90 transition-opacity cursor-pointer`}
              style={{ width: `${(item.duration / totalDuration) * 100}%` }}
              title={`${item.title || item.type}: ${item.duration}s`}
            ></div>
          ))}
        </div>
        <div className="flex justify-between text-xs text-gray-400 mt-1">
          <span>开始</span>
          <span>结束</span>
        </div>
      </div>

      {/* Timeline Items */}
      <div className="space-y-3 max-h-64 overflow-y-auto">
        {timelineItems.map((item, index) => (
          <div
            key={index}
            className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-200 border border-gray-200"
          >
            <div className={`w-10 h-10 rounded-full ${item.color} flex items-center justify-center shadow-sm`}>
              <span className="material-icons text-white text-sm">{item.icon}</span>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">
                  {item.title || item.type}
                </span>
                <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">
                  {item.duration}s
                </span>
              </div>
              {item.description && (
                <p className="text-xs text-gray-600 truncate mt-1">
                  {item.description}
                </p>
              )}
            </div>
            <button className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded">
              <span className="material-icons text-sm">more_vert</span>
            </button>
          </div>
        ))}
      </div>

      {/* Timeline Controls */}
      <div className="border-t border-gray-200 pt-4 mt-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <button className="w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors">
              <span className="material-icons text-sm">skip_previous</span>
            </button>
            <button className="w-8 h-8 bg-primary hover:bg-blue-600 text-white rounded-full flex items-center justify-center transition-colors">
              <span className="material-icons text-sm">play_arrow</span>
            </button>
            <button className="w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors">
              <span className="material-icons text-sm">skip_next</span>
            </button>
          </div>
          
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <span className="material-icons text-sm">schedule</span>
            <span>总时长: {Math.floor(totalDuration / 60)}:{(totalDuration % 60).toFixed(0).padStart(2, '0')}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompositionTimeline;
