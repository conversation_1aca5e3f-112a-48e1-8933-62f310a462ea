import React, { useState } from 'react';

const VideoPreview = ({ videoUrl, isComposed }) => {
  const [isPlaying, setIsPlaying] = useState(false);

  if (!isComposed) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          视频预览
        </h3>
        <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <span className="material-icons text-6xl text-gray-300 mb-4 block">
              movie_creation
            </span>
            <p className="text-gray-500">
              视频合成完成后将在此显示预览
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          最终视频预览
        </h3>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            <span className="text-sm text-gray-600">已合成</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">质量:</span>
            <span className="text-sm font-medium text-primary">1080p</span>
          </div>
        </div>
      </div>
      
      {/* Video Player */}
      <div className="relative aspect-video bg-black rounded-lg overflow-hidden mb-4">
        <video
          controls
          poster="https://images.unsplash.com/photo-1574717523213-2a6285a2b450?w=600&h=338&fit=crop&q=80"
          className="w-full h-full"
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
        >
          <source src={videoUrl} type="video/mp4" />
          您的浏览器不支持视频播放。
        </video>
        
        {/* Custom Controls Overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-4">
          <div className="flex items-center gap-3 text-white">
            <button className="hover:text-primary transition-colors">
              <span className="material-icons">
                {isPlaying ? 'pause' : 'play_arrow'}
              </span>
            </button>
            <div className="flex-1 bg-white/20 rounded-full h-1">
              <div className="bg-primary h-1 rounded-full" style={{ width: '30%' }}></div>
            </div>
            <span className="text-sm">1:25 / 2:30</span>
            <button className="hover:text-primary transition-colors">
              <span className="material-icons">fullscreen</span>
            </button>
          </div>
        </div>
      </div>

      {/* Video Info */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center mb-6">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
          <div className="text-xl font-bold text-blue-700">2:30</div>
          <div className="text-sm text-blue-600">总时长</div>
        </div>
        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
          <div className="text-xl font-bold text-green-700">1080p</div>
          <div className="text-sm text-green-600">分辨率</div>
        </div>
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200">
          <div className="text-xl font-bold text-purple-700">30fps</div>
          <div className="text-sm text-purple-600">帧率</div>
        </div>
        <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg p-4 border border-orange-200">
          <div className="text-xl font-bold text-orange-700">45MB</div>
          <div className="text-sm text-orange-600">文件大小</div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-3 gap-3">
        <button className="bg-blue-50 hover:bg-blue-100 text-blue-700 px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center justify-center gap-2 border border-blue-200">
          <span className="material-icons text-sm">play_arrow</span>
          播放
        </button>
        <button className="bg-green-50 hover:bg-green-100 text-green-700 px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center justify-center gap-2 border border-green-200">
          <span className="material-icons text-sm">download</span>
          下载
        </button>
        <button className="bg-purple-50 hover:bg-purple-100 text-purple-700 px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center justify-center gap-2 border border-purple-200">
          <span className="material-icons text-sm">share</span>
          分享
        </button>
      </div>
    </div>
  );
};

export default VideoPreview;
