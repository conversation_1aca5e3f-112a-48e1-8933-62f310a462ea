import { useState } from 'react';
import Button from '../common/Button';

const CompositionSettings = ({ onRecompose }) => {
  const [settings, setSettings] = useState({
    transition: 'fade',
    speed: 'normal',
  });

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const options = {
    transition: [
      { value: 'fade', label: '淡入淡出' },
      { value: 'slide', label: '滑动' },
      { value: 'cut', label: '直切' }
    ],
    speed: [
      { value: 'slow', label: '慢速' },
      { value: 'normal', label: '正常' },
      { value: 'fast', label: '快速' }
    ]
  };

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <span className="material-icons text-primary">tune</span>
        合成设置
      </h3>
      
      <div className="space-y-6">
        {Object.entries(options).map(([key, values]) => (
          <div key={key}>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {key === 'transition' && '转场效果'}
              {key === 'speed' && '播放速度'}
            </label>
            <div className="space-y-2">
              {values.map((option) => (
                <label
                  key={option.value}
                  className="flex items-center cursor-pointer"
                >
                  <input
                    type="radio"
                    name={key}
                    value={option.value}
                    checked={settings[key] === option.value}
                    onChange={() => handleSettingChange(key, option.value)}
                    className="w-4 h-4 text-primary focus:ring-primary border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    {option.label}
                  </span>
                </label>
              ))}
            </div>
          </div>
        ))}


        {/* Apply Settings Button */}
        <Button
          variant="secondary"
          onClick={onRecompose}
          className="w-full"
        >
          应用设置并重新合成
        </Button>
      </div>
    </div>
  );
};

export default CompositionSettings;
