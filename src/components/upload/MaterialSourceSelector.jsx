import { useApp } from '../../context/AppContext.jsx';

const MaterialSourceSelector = () => {
  const { state, dispatch, ActionTypes } = useApp();

  const handleSourceChange = (source) => {
    dispatch({ type: ActionTypes.SET_MATERIAL_SOURCE, payload: source });
  };

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        素材来源
      </label>
      <div className="flex gap-5 bg-gray-50 p-4 rounded-lg border border-gray-200 w-fit">
        <label className="flex items-center gap-2 cursor-pointer">
          <input
            type="radio"
            name="material-source"
            value="upload"
            checked={state.materialSource === 'upload'}
            onChange={() => handleSourceChange('upload')}
            className="w-4 h-4 text-primary focus:ring-primary"
          />
          <span className="font-medium text-gray-700">上传文件</span>
        </label>
        <label className="flex items-center gap-2 cursor-pointer">
          <input
            type="radio"
            name="material-source"
            value="text"
            checked={state.materialSource === 'text'}
            onChange={() => handleSourceChange('text')}
            className="w-4 h-4 text-primary focus:ring-primary"
          />
          <span className="font-medium text-gray-700">输入文本</span>
        </label>
      </div>
    </div>
  );
};

export default MaterialSourceSelector;
