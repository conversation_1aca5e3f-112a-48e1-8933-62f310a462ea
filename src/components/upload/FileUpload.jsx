import { useRef } from 'react';
import { useFileUpload } from '../../hooks/useFileUpload';

const FileUpload = () => {
  const fileInputRef = useRef(null);
  const {
    uploadedFiles,
    isDragOver,
    handleFileSelect,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    removeFile
  } = useFileUpload();

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (e) => {
    if (e.target.files.length > 0) {
      handleFileSelect(e.target.files);
    }
  };

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        上传素材
      </label>
      
      {/* Upload Area */}
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-5 bg-gray-50">
        <div
          className={`
            p-10 text-center cursor-pointer rounded-lg transition-all duration-300
            ${isDragOver 
              ? 'border-primary bg-blue-50 border-2 border-dashed' 
              : 'hover:bg-gray-100'
            }
          `}
          onClick={handleUploadClick}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <span className="material-icons text-5xl text-primary mb-4 block">
            cloud_upload
          </span>
          <p className="text-base text-gray-600 mb-2">
            拖拽文件到此处或点击上传
          </p>
          <span className="text-sm text-gray-400">
            支持文档格式
          </span>
          
          <input
            ref={fileInputRef}
            type="file"
            multiple
            className="hidden"
            onChange={handleFileInputChange}
          />
        </div>
        
        {/* Uploaded Files List */}
        {uploadedFiles.length > 0 && (
          <div className="mt-5 space-y-3">
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="flex items-center justify-between bg-gray-50 p-3 rounded-md border border-gray-200"
              >
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-700" title={file.name}>
                    {file.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {file.formattedSize}
                  </div>
                </div>
                <button
                  onClick={() => removeFile(file.id)}
                  className="text-gray-400 hover:text-red-500 text-xl font-bold px-2 transition-colors duration-200"
                  title="删除文件"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default FileUpload;
