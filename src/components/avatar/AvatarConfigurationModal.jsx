import React, { useState } from 'react';
import Modal from '../common/Modal';
import Button from '../common/Button';

const AvatarConfigurationModal = ({ isOpen, onClose, shotData, onConfigure }) => {
  const [settings, setSettings] = useState({
    position: 'center',
    size: 'medium',
    background: 'transparent',
    lighting: 'natural',
    expression: 'neutral',
    gestures: true,
    eyeTracking: true,
    lipSync: true,
    customPrompt: ''
  });

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleConfigure = () => {
    if (shotData?.shotId) {
      onConfigure(shotData.shotId);
    }
    onClose();
  };

  const handleCancel = () => {
    setSettings({
      position: 'center',
      size: 'medium',
      background: 'transparent',
      lighting: 'natural',
      expression: 'neutral',
      gestures: true,
      eyeTracking: true,
      lipSync: true,
      customPrompt: ''
    });
    onClose();
  };

  if (!shotData) return null;

  const options = {
    position: [
      { value: 'left', label: '左侧', description: '数字人出现在画面左侧' },
      { value: 'center', label: '居中', description: '数字人出现在画面中央' },
      { value: 'right', label: '右侧', description: '数字人出现在画面右侧' }
    ],
    size: [
      { value: 'small', label: '小尺寸', description: '占画面1/4大小' },
      { value: 'medium', label: '中等尺寸', description: '占画面1/2大小' },
      { value: 'large', label: '大尺寸', description: '占画面3/4大小' }
    ],
    background: [
      { value: 'transparent', label: '透明背景', description: '与原画面融合' },
      { value: 'white', label: '白色背景', description: '纯白色背景' },
      { value: 'gradient', label: '渐变背景', description: '美观渐变效果' }
    ],
    lighting: [
      { value: 'natural', label: '自然光照', description: '模拟自然光线' },
      { value: 'studio', label: '摄影棚光照', description: '专业摄影棚效果' },
      { value: 'soft', label: '柔光效果', description: '温和柔和光线' }
    ],
    expression: [
      { value: 'neutral', label: '中性表情', description: '自然平和表情' },
      { value: 'smile', label: '微笑表情', description: '友好亲切微笑' },
      { value: 'serious', label: '严肃表情', description: '专业严肃表情' },
      { value: 'gentle', label: '温和表情', description: '温和亲切表情' }
    ]
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleCancel}
      title="自定义数字人设置"
      size="md"
      className="max-h-[90vh] overflow-y-auto"
      footer={
        <>
          <Button variant="secondary" onClick={handleCancel}>
            取消
          </Button>
          <Button variant="primary" onClick={handleConfigure}>
            配置数字人
          </Button>
        </>
      }
    >
      <div className="space-y-4">
        {/* Shot Preview */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            当前镜头
          </label>
          <div className="flex gap-3 p-3 bg-gray-50 rounded-lg">
            <img
              src={shotData.shot?.imageUrl}
              alt="镜头预览"
              className="w-16 h-10 object-cover rounded flex-shrink-0"
            />
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-700 leading-relaxed line-clamp-2">
                {shotData.shot?.scriptText}
              </p>
            </div>
          </div>
        </div>

        {/* Avatar Settings - Compact Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(options).map(([key, values]) => (
            <div key={key}>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {key === 'position' && '位置设置'}
                {key === 'size' && '大小设置'}
                {key === 'background' && '背景设置'}
                {key === 'lighting' && '光照设置'}
                {key === 'expression' && '表情设置'}
              </label>
              <div className="space-y-1">
                {values.map((option) => (
                  <label
                    key={option.value}
                    className={`
                      flex items-center p-2 border rounded cursor-pointer transition-all duration-200
                      ${settings[key] === option.value
                        ? 'border-primary bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                      }
                    `}
                  >
                    <input
                      type="radio"
                      name={key}
                      value={option.value}
                      checked={settings[key] === option.value}
                      onChange={() => handleSettingChange(key, option.value)}
                      className="w-4 h-4 text-primary focus:ring-primary border-gray-300 mr-2"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900">
                        {option.label}
                      </div>
                      <div className="text-xs text-gray-500 truncate">
                        {option.description}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Advanced Settings */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            高级功能
          </label>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div>
                <span className="text-sm font-medium text-gray-700">手势动作</span>
                <p className="text-xs text-gray-500">自动添加手势和肢体动作</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer" 
                  checked={settings.gestures}
                  onChange={(e) => handleSettingChange('gestures', e.target.checked)}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
            
            <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div>
                <span className="text-sm font-medium text-gray-700">眼神跟随</span>
                <p className="text-xs text-gray-500">眼神自然跟随内容变化</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer" 
                  checked={settings.eyeTracking}
                  onChange={(e) => handleSettingChange('eyeTracking', e.target.checked)}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
            
            <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div>
                <span className="text-sm font-medium text-gray-700">口型同步</span>
                <p className="text-xs text-gray-500">口型与配音完美同步</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer" 
                  checked={settings.lipSync}
                  onChange={(e) => handleSettingChange('lipSync', e.target.checked)}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
          </div>
        </div>

        {/* Custom Prompt */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            自定义要求 (可选)
          </label>
          <textarea
            value={settings.customPrompt}
            onChange={(e) => handleSettingChange('customPrompt', e.target.value)}
            placeholder="例如：特定的动作、表情变化、互动方式等..."
            rows={2}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
          />
          <p className="text-xs text-gray-500 mt-1">
            描述您对这个数字人的特殊要求
          </p>
        </div>

        {/* Preview Settings - Compact */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="text-sm font-medium text-gray-700 mb-2">当前设置预览</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-600">位置:</span>
              <span className="text-gray-900 font-medium">
                {options.position.find(p => p.value === settings.position)?.label}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">大小:</span>
              <span className="text-gray-900 font-medium">
                {options.size.find(s => s.value === settings.size)?.label}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">背景:</span>
              <span className="text-gray-900 font-medium">
                {options.background.find(b => b.value === settings.background)?.label}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">表情:</span>
              <span className="text-gray-900 font-medium">
                {options.expression.find(e => e.value === settings.expression)?.label}
              </span>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default AvatarConfigurationModal;
