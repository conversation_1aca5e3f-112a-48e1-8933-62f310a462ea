import { useState } from 'react';
import Button from '../common/Button';
import AvatarPreviewModal from '../common/AvatarPreviewModal.jsx';

const AvatarCard = ({ 
  shot, 
  index, 
  avatarSettings, 
  isConfiguring, 
  onAvatarSelect, 
  onConfigure, 
  onCustomize 
}) => {
  const [showAvatarOptions, setShowAvatarOptions] = useState(false);
  const [showAvatarPreview, setShowAvatarPreview] = useState(false);

  const avatarTypes = [
    {
      id: 'emma',
      name: 'Emma',
      description: '专业商务',
      imageUrl: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&q=80',
      style: '商务',
      gender: '女性'
    },
    {
      id: 'david',
      name: 'David',
      description: '休闲亲和',
      imageUrl: 'https://picsum.photos/seed/david/150/150',
      style: '休闲',
      gender: '男性'
    },
    {
      id: 'chloe',
      name: 'Chloe',
      description: '时尚科技',
      imageUrl: 'https://picsum.photos/seed/chloe/150/150',
      style: '时尚',
      gender: '女性'
    },
    {
      id: 'mark',
      name: 'Mark',
      description: '成熟稳重',
      imageUrl: 'https://picsum.photos/seed/mark/150/150',
      style: '成熟',
      gender: '男性'
    },
    {
      id: 'sophia',
      name: 'Sophia',
      description: '年轻活力',
      imageUrl: 'https://picsum.photos/seed/sophia/150/150',
      style: '活力',
      gender: '女性'
    },
    {
      id: 'alex',
      name: 'Alex',
      description: '创意艺术',
      imageUrl: 'https://picsum.photos/seed/alex/150/150',
      style: '创意',
      gender: '男性'
    }
  ];

  const selectedAvatar = avatarTypes.find(a => a.id === avatarSettings.avatarType) || avatarTypes[0];

  const handleAvatarSelect = (avatarType) => {
    onAvatarSelect(avatarType);
    setShowAvatarOptions(false);
  };

  const handlePreviewAvatar = () => {
    setShowAvatarPreview(true);
  };

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-lg transition-all duration-300 group">
      {/* Mobile: Vertical Layout, Desktop: Horizontal Layout */}
      <div className="flex flex-col md:flex-row">
        {/* Left: Avatar Preview (45% on desktop) */}
        <div className="relative h-[220px] md:h-[200px] md:w-[45%] flex-shrink-0 bg-gradient-to-br from-blue-50 to-purple-50 p-2">
          {avatarSettings.isConfigured ? (
            <div className="relative w-full h-full flex items-center justify-center rounded-md overflow-hidden">
              <img
                src={avatarSettings.previewUrl || selectedAvatar.imageUrl}
                alt={`数字人 ${selectedAvatar.name}`}
                className="w-full h-full object-contain"
              />
              {/* Overlay with shot background */}
              <div className="absolute inset-0 bg-black bg-opacity-20 flex items-end p-2">
                <div className="bg-white bg-opacity-90 rounded px-2 py-1 text-xs text-gray-700">
                  {selectedAvatar.name} - {selectedAvatar.description}
                </div>
              </div>
            </div>
          ) : (
            <div className="w-full h-full flex items-center justify-center relative rounded-md overflow-hidden">
              <img
                src={shot.imageUrl}
                alt={`镜头 ${index + 1} 背景`}
                className="w-full h-full object-contain opacity-30"
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <img
                    src={selectedAvatar.imageUrl}
                    alt={selectedAvatar.name}
                    className="w-16 h-16 rounded-full mx-auto mb-2 object-cover border-2 border-white shadow-lg"
                  />
                  <div className="bg-white bg-opacity-90 rounded px-2 py-1 text-xs text-gray-700">
                    {selectedAvatar.name}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Shot Number Badge */}
          <div className="absolute top-1 left-1 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-semibold shadow-lg">
            {index + 1}
          </div>

          {/* Avatar Status Badge */}
          <div className="absolute top-1 right-1">
            {avatarSettings.isConfigured ? (
              <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs flex items-center gap-1 shadow-lg">
                <span className="material-icons text-sm">check_circle</span>
                已配置
              </div>
            ) : isConfiguring ? (
              <div className="bg-blue-500 text-white px-2 py-1 rounded-full text-xs flex items-center gap-1 shadow-lg">
                <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                配置中
              </div>
            ) : (
              <div className="bg-gray-500 text-white px-2 py-1 rounded-full text-xs shadow-lg">
                待配置
              </div>
            )}
          </div>

          {/* Preview Button Overlay (when configured) */}
          {avatarSettings.isConfigured && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <button
                onClick={handlePreviewAvatar}
                className="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all duration-200"
              >
                <span className="material-icons text-2xl text-gray-700">visibility</span>
              </button>
            </div>
          )}
        </div>

        {/* Right: Content (55% on desktop) */}
        <div className="p-4 md:p-6 flex-1 flex flex-col">
          {/* Header */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-900 mb-2">
              镜头 {index + 1}
            </h3>
            {/* Script Text */}
            <p className="text-sm text-gray-600 leading-relaxed line-clamp-3">
              {shot.scriptText}
            </p>
          </div>

          {/* Avatar Selection */}
          <div className="mb-4">
            <label className="block text-xs font-medium text-gray-700 mb-2">
              数字人类型
            </label>
            <div className="relative">
              <button
                onClick={() => setShowAvatarOptions(!showAvatarOptions)}
                className="w-full flex items-center justify-between p-2 border border-gray-200 rounded-md hover:border-gray-300 transition-colors duration-200"
              >
                <div className="flex items-center gap-2">
                  <img
                    src={selectedAvatar.imageUrl}
                    alt={selectedAvatar.name}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                  <div className="text-left">
                    <div className="text-sm font-medium text-gray-900">
                      {selectedAvatar.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {selectedAvatar.style} · {selectedAvatar.gender}
                    </div>
                  </div>
                </div>
                <span className="material-icons text-gray-400">
                  {showAvatarOptions ? 'expand_less' : 'expand_more'}
                </span>
              </button>

              {/* Avatar Options Dropdown */}
              {showAvatarOptions && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 max-h-48 overflow-y-auto">
                  {avatarTypes.map((avatar) => (
                    <button
                      key={avatar.id}
                      onClick={() => handleAvatarSelect(avatar.id)}
                      className={`
                        w-full flex items-center gap-2 p-2 text-left hover:bg-gray-50 transition-colors duration-200
                        ${avatar.id === selectedAvatar.id ? 'bg-blue-50 text-primary' : 'text-gray-700'}
                      `}
                    >
                      <img
                        src={avatar.imageUrl}
                        alt={avatar.name}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                      <div className="flex-1">
                        <div className="text-sm font-medium">
                          {avatar.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {avatar.style} · {avatar.gender}
                        </div>
                      </div>
                      {avatar.id === selectedAvatar.id && (
                        <span className="material-icons text-primary">check</span>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Configuration Info (when configured) */}
          {avatarSettings.isConfigured && (
            <div className="bg-gray-50 rounded-lg p-3 mb-4">
              <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                <div className="flex justify-between">
                  <span>位置:</span>
                  <span className="text-gray-900 font-medium">
                    {avatarSettings.position === 'center' ? '居中' :
                     avatarSettings.position === 'left' ? '左侧' : '右侧'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>大小:</span>
                  <span className="text-gray-900 font-medium">
                    {avatarSettings.size === 'small' ? '小' :
                     avatarSettings.size === 'medium' ? '中' : '大'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>背景:</span>
                  <span className="text-gray-900 font-medium">
                    {avatarSettings.background === 'transparent' ? '透明' :
                     avatarSettings.background === 'white' ? '白色' : '渐变'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>表情:</span>
                  <span className="text-gray-900 font-medium">
                    {avatarSettings.expression === 'neutral' ? '中性' :
                     avatarSettings.expression === 'smile' ? '微笑' : '严肃'}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 mt-auto">
            {avatarSettings.isConfigured ? (
              <>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={onConfigure}
                  className="flex-1"
                >
                  重新配置
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={onCustomize}
                  className="px-3"
                >
                  <span className="material-icons text-sm">tune</span>
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={onConfigure}
                  disabled={isConfiguring}
                  className="flex-1"
                >
                  {isConfiguring ? (
                    <>
                      <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin mr-1"></div>
                      配置中
                    </>
                  ) : (
                    '配置数字人'
                  )}
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={onCustomize}
                  className="px-3"
                >
                  <span className="material-icons text-sm">tune</span>
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Avatar Preview Modal */}
      <AvatarPreviewModal
        isOpen={showAvatarPreview}
        onClose={() => setShowAvatarPreview(false)}
        avatarData={{
          ...selectedAvatar,
          ...avatarSettings,
          previewUrl: avatarSettings.previewUrl
        }}
        title={`镜头 ${index + 1} - ${selectedAvatar.name}`}
      />
    </div>
  );
};

export default AvatarCard;
