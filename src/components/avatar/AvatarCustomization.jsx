import React, { useState } from 'react';
import Button from '../common/Button';

const AvatarCustomization = ({ isConfiguring, onConfigure }) => {
  const [settings, setSettings] = useState({
    position: 'center',
    size: 'medium',
    background: 'transparent',
    lighting: 'natural',
    expression: 'neutral'
  });

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const options = {
    position: [
      { value: 'left', label: '左侧' },
      { value: 'center', label: '居中' },
      { value: 'right', label: '右侧' }
    ],
    size: [
      { value: 'small', label: '小' },
      { value: 'medium', label: '中' },
      { value: 'large', label: '大' }
    ],
    background: [
      { value: 'transparent', label: '透明' },
      { value: 'white', label: '白色' },
      { value: 'gradient', label: '渐变' }
    ],
    lighting: [
      { value: 'natural', label: '自然光' },
      { value: 'studio', label: '摄影棚' },
      { value: 'soft', label: '柔光' }
    ],
    expression: [
      { value: 'neutral', label: '中性' },
      { value: 'smile', label: '微笑' },
      { value: 'serious', label: '严肃' }
    ]
  };

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">数字人配置</h3>
      
      <div className="space-y-6">
        {Object.entries(options).map(([key, values]) => (
          <div key={key}>
            <label className="block text-sm font-medium text-gray-700 mb-2 capitalize">
              {key === 'position' && '位置'}
              {key === 'size' && '大小'}
              {key === 'background' && '背景'}
              {key === 'lighting' && '光照'}
              {key === 'expression' && '表情'}
            </label>
            <div className="grid grid-cols-3 gap-2">
              {values.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleSettingChange(key, option.value)}
                  className={`
                    px-3 py-2 text-sm rounded-md border transition-colors duration-200
                    ${settings[key] === option.value
                      ? 'border-primary bg-blue-50 text-primary'
                      : 'border-gray-200 hover:border-gray-300 text-gray-700'
                    }
                  `}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        ))}

        {/* Advanced Settings */}
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">高级设置</h4>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">手势动作</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" className="sr-only peer" defaultChecked />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">眼神跟随</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" className="sr-only peer" defaultChecked />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">口型同步</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" className="sr-only peer" defaultChecked />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
          </div>
        </div>

        {/* Configure Button */}
        <Button
          variant="primary"
          onClick={onConfigure}
          disabled={isConfiguring}
          className="w-full"
        >
          {isConfiguring ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              正在配置数字人...
            </>
          ) : (
            '应用配置'
          )}
        </Button>
      </div>
    </div>
  );
};

export default AvatarCustomization;
