import { useState, useEffect } from 'react';
import { useApp } from '../../context/AppContext.jsx';
import { useNavigation } from '../../hooks/useNavigation';
import Button from '../common/Button';
import AvatarCard from './AvatarCard.jsx';
import AvatarConfigurationModal from './AvatarConfigurationModal.jsx';
import { useModal } from '../../hooks/useModal';

const AvatarConfiguration = () => {
  const { state, dispatch, ActionTypes } = useApp();
  const { setCurrentTab } = useNavigation();
  const [shotAvatars, setShotAvatars] = useState({});
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [configuringShots, setConfiguringShots] = useState(new Set());
  const { isOpen, modalData, openModal, closeModal } = useModal();

  // 初始化每个镜头的数字人设置
  useEffect(() => {
    if (state.shots.length > 0) {
      const initialAvatars = {};
      state.shots.forEach(shot => {
        if (!shotAvatars[shot.id]) {
          initialAvatars[shot.id] = {
            avatarType: 'emma',
            position: 'center',
            size: 'medium',
            background: 'transparent',
            lighting: 'natural',
            expression: 'neutral',
            isConfigured: false,
            previewUrl: null
          };
        }
      });
      setShotAvatars(prev => ({ ...prev, ...initialAvatars }));
    }
  }, [state.shots]);

  const handleAvatarSelect = (shotId, avatarType) => {
    setShotAvatars(prev => ({
      ...prev,
      [shotId]: {
        ...prev[shotId],
        avatarType,
        isConfigured: false,
        previewUrl: null
      }
    }));
  };

  const handleConfigureAvatar = async (shotId) => {
    setConfiguringShots(prev => new Set([...prev, shotId]));

    // 模拟数字人配置过程
    setTimeout(() => {
      setShotAvatars(prev => ({
        ...prev,
        [shotId]: {
          ...prev[shotId],
          isConfigured: true,
          previewUrl: `https://picsum.photos/seed/avatar-${shotId}/300/400`
        }
      }));
      setConfiguringShots(prev => {
        const newSet = new Set(prev);
        newSet.delete(shotId);
        return newSet;
      });
    }, 2000 + Math.random() * 2000);
  };

  const handleConfigureAll = async () => {
    setIsConfiguring(true);

    // 为所有镜头配置数字人
    const promises = state.shots.map(shot =>
      new Promise(resolve => {
        setTimeout(() => {
          setShotAvatars(prev => ({
            ...prev,
            [shot.id]: {
              ...prev[shot.id],
              isConfigured: true,
              previewUrl: `https://picsum.photos/seed/avatar-${shot.id}/300/400`
            }
          }));
          resolve();
        }, 1000 + Math.random() * 3000);
      })
    );

    await Promise.all(promises);
    dispatch({ type: ActionTypes.CONFIGURE_AVATAR });
    setIsConfiguring(false);
  };

  const handleContinue = () => {
    setCurrentTab('compose');
  };

  const allAvatarsConfigured = state.shots.every(shot =>
    shotAvatars[shot.id]?.isConfigured
  );

  if (state.shots.length === 0) {
    return (
      <div>
        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            添加数字人
          </h2>
          <p className="text-gray-600">
            为每个镜头选择和配置数字人形象，让您的视频更加生动和专业
          </p>
        </div>

        <div className="text-center py-12">
          <span className="material-icons text-6xl text-gray-300 mb-4 block">
            person_outline
          </span>
          <p className="text-gray-500 text-lg">
            请先在&quot;镜头编辑&quot;页面创建镜头，才能添加对应的数字人。
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          添加数字人
        </h2>
        <p className="text-gray-600">
          为每个镜头选择和配置数字人形象，让您的视频更加生动和专业
        </p>
      </div>

      {/* Avatar Cards Grid */}
      <div className="grid grid-cols-1 gap-6 mb-8">
        {state.shots.map((shot, index) => (
          <AvatarCard
            key={shot.id}
            shot={shot}
            index={index}
            avatarSettings={shotAvatars[shot.id] || {}}
            isConfiguring={configuringShots.has(shot.id)}
            onAvatarSelect={(avatarType) => handleAvatarSelect(shot.id, avatarType)}
            onConfigure={() => handleConfigureAvatar(shot.id)}
            onCustomize={() => openModal({ shotId: shot.id, shot })}
          />
        ))}
      </div>

      {/* Batch Actions */}
      <div className="bg-white rounded-lg p-6 shadow-sm mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              批量操作
            </h3>
            <p className="text-gray-600">
              为所有镜头快速配置数字人，或统一设置数字人类型
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="secondary"
              onClick={() => {
                // 统一设置所有镜头为Emma
                state.shots.forEach(shot => {
                  handleAvatarSelect(shot.id, 'emma');
                });
              }}
            >
              统一设置
            </Button>
            <Button
              variant="primary"
              onClick={handleConfigureAll}
              disabled={isConfiguring}
            >
              {isConfiguring ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  正在配置全部数字人...
                </>
              ) : (
                '配置全部数字人'
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      {allAvatarsConfigured && (
        <div className="flex justify-end">
          <Button
            variant="primary"
            onClick={handleContinue}
          >
            确认数字人并进入视频合成
          </Button>
        </div>
      )}

      {/* Avatar Configuration Modal */}
      <AvatarConfigurationModal
        isOpen={isOpen}
        onClose={closeModal}
        shotData={modalData}
        onConfigure={handleConfigureAvatar}
      />
    </div>
  );
};

export default AvatarConfiguration;
