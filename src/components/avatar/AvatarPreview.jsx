import { useApp } from '../../context/AppContext.jsx';

const AvatarPreview = ({ isConfigured, isConfiguring }) => {
  const { state } = useApp();

  if (isConfiguring) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          数字人配置中
        </h3>
        <div className="flex flex-col items-center justify-center py-12">
          <div className="w-16 h-16 border-4 border-gray-200 border-t-primary rounded-full animate-spin mb-4"></div>
          <p className="text-gray-600 text-center">
            正在应用您的配置设置，生成专属数字人...
          </p>
        </div>
      </div>
    );
  }

  if (!isConfigured) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          数字人预览
        </h3>
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <span className="material-icons text-6xl text-gray-300 mb-4">
            person_outline
          </span>
          <p className="text-gray-500">
            选择数字人形象并配置参数后，这里将显示预览效果
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        数字人预览
      </h3>
      
      {/* Preview Video */}
      <div className="mb-6">
        <div className="aspect-video bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex items-center justify-center relative overflow-hidden">
          {/* Avatar Image */}
          <div className="absolute inset-0 flex items-center justify-center">
            <img
              src={`https://picsum.photos/seed/${state.selectedAvatar}/300/400`}
              alt="数字人预览"
              className="h-full object-cover"
            />
          </div>
          
          {/* Play Button Overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
            <button className="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all duration-200">
              <span className="material-icons text-3xl text-gray-700 ml-1">play_arrow</span>
            </button>
          </div>
        </div>
      </div>

      {/* Configuration Summary */}
      <div className="bg-gray-50 rounded-lg p-4 mb-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">当前配置</h4>
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="flex justify-between">
            <span className="text-gray-500">位置:</span>
            <span className="text-gray-700">居中</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">大小:</span>
            <span className="text-gray-700">中等</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">背景:</span>
            <span className="text-gray-700">透明</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">光照:</span>
            <span className="text-gray-700">自然光</span>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2">
        <button className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md text-sm transition-colors duration-200">
          重新配置
        </button>
        <button className="flex-1 bg-primary hover:bg-blue-600 text-white px-3 py-2 rounded-md text-sm transition-colors duration-200">
          测试动画
        </button>
      </div>
    </div>
  );
};

export default AvatarPreview;
