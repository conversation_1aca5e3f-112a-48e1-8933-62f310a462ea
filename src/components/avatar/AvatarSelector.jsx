import React from 'react';
import { useApp } from '../../context/AppContext.jsx';

const AvatarSelector = () => {
  const { state, dispatch, ActionTypes } = useApp();

  const avatars = [
    {
      id: 'emma',
      name: 'Emma',
      description: '专业商务形象',
      imageUrl: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&q=80',
      style: '商务',
      gender: '女性'
    },
    {
      id: 'david',
      name: '<PERSON>',
      description: '休闲亲和形象',
      imageUrl: 'https://picsum.photos/seed/david/150/150',
      style: '休闲',
      gender: '男性'
    },
    {
      id: 'chloe',
      name: 'Chloe',
      description: '时尚科技感',
      imageUrl: 'https://picsum.photos/seed/chloe/150/150',
      style: '时尚',
      gender: '女性'
    },
    {
      id: 'mark',
      name: '<PERSON>',
      description: '成熟稳重风格',
      imageUrl: 'https://picsum.photos/seed/mark/150/150',
      style: '成熟',
      gender: '男性'
    },
    {
      id: 'sophia',
      name: 'Sophia',
      description: '年轻活力形象',
      imageUrl: 'https://picsum.photos/seed/sophia/150/150',
      style: '活力',
      gender: '女性'
    },
    {
      id: 'alex',
      name: 'Alex',
      description: '创意艺术风格',
      imageUrl: 'https://picsum.photos/seed/alex/150/150',
      style: '创意',
      gender: '男性'
    }
  ];

  const handleAvatarSelect = (avatarId) => {
    dispatch({ type: ActionTypes.SET_SELECTED_AVATAR, payload: avatarId });
  };

  const handlePreview = (e, avatarId) => {
    e.stopPropagation();
    console.log(`Previewing avatar: ${avatarId}`);
  };

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">选择数字人形象</h3>
      
      <div className="grid grid-cols-2 gap-4">
        {avatars.map((avatar) => {
          const isSelected = state.selectedAvatar === avatar.id;
          
          return (
            <div
              key={avatar.id}
              onClick={() => handleAvatarSelect(avatar.id)}
              className={`
                p-3 rounded-lg border-2 cursor-pointer transition-all duration-300 hover:shadow-md text-center
                ${isSelected 
                  ? 'border-primary bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
                }
              `}
            >
              <img
                src={avatar.imageUrl}
                alt={avatar.name}
                className="w-16 h-16 rounded-full mx-auto mb-2 object-cover bg-gray-100"
              />
              
              <h4 className="font-medium text-gray-900 text-sm mb-1">
                {avatar.name}
              </h4>
              
              <div className="flex justify-center gap-1 mb-2">
                <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                  {avatar.style}
                </span>
                <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                  {avatar.gender}
                </span>
              </div>
              
              <p className="text-xs text-gray-600 mb-2">
                {avatar.description}
              </p>
              
              <button
                onClick={(e) => handlePreview(e, avatar.id)}
                className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-full transition-colors duration-200"
              >
                预览
              </button>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AvatarSelector;
