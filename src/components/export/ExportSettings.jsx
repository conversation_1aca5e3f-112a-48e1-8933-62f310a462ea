import { useApp } from '../../context/AppContext.jsx';

const ExportSettings = () => {
  const { state, dispatch, ActionTypes } = useApp();

  const handleSettingChange = (setting, value) => {
    dispatch({
      type: ActionTypes.UPDATE_EXPORT_SETTINGS,
      payload: { [setting]: value }
    });
  };

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">导出设置</h3>
      
      <div className="space-y-4">
        {/* Video Quality */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            视频质量
          </label>
          <select
            value={state.exportSettings.quality}
            onChange={(e) => handleSettingChange('quality', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="1080p">1080p</option>
            <option value="720p">720p</option>
            <option value="480p">480p</option>
          </select>
        </div>

        {/* File Format */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            文件格式
          </label>
          <select
            value={state.exportSettings.format}
            onChange={(e) => handleSettingChange('format', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="MP4">MP4</option>
            <option value="MOV">MOV</option>
            <option value="AVI">AVI</option>
          </select>
        </div>
      </div>
    </div>
  );
};

export default ExportSettings;
