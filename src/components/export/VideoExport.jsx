import Button from '../common/Button';
import ExportSettings from './ExportSettings';

const VideoExport = () => {

  const handleSaveProject = () => {
    console.log('Saving project...');
    // Implement save project logic
  };

  const handleDownloadVideo = () => {
    console.log('Downloading video...');
    // Implement download logic
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          导出视频
        </h2>
        <p className="text-gray-600">
          您的视频已生成完成，可以预览并导出
        </p>
      </div>

      <div className="bg-white rounded-lg p-8 shadow-sm">
        {/* Final Preview */}
        <div className="mb-8">
          <video
            controls
            poster="https://images.unsplash.com/photo-1517649763942-7e3c76255010?w=600&h=338&fit=crop&q=80"
            className="w-full rounded-lg"
          >
            <source src="" type="video/mp4" />
            您的浏览器不支持视频播放。
          </video>
        </div>

        {/* Export Settings */}
        <div className="mb-8">
          <ExportSettings />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-4">
          <Button 
            variant="secondary" 
            onClick={handleSaveProject}
          >
            保存项目
          </Button>
          <Button 
            variant="primary" 
            onClick={handleDownloadVideo}
          >
            下载视频
          </Button>
        </div>
      </div>
    </div>
  );
};

export default VideoExport;
