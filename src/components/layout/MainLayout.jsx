import Sidebar from './Sidebar';
import StatusBar from './StatusBar';
import { useNavigation } from '../../hooks/useNavigation';

// Import tab components
import UploadContent from '../upload/UploadContent';
import ScriptGeneration from '../script/ScriptGeneration';
import StoryboardEditor from '../storyboard/StoryboardEditor';
import VoiceGeneration from '../voice-generation/VoiceGeneration';
import VideoGeneration from '../video-generation/VideoGeneration';
import AvatarConfiguration from '../avatar/AvatarConfiguration';
import VideoCompose from '../video-compose/VideoCompose';
import VideoExport from '../export/VideoExport';

const MainLayout = () => {
  const { currentTab } = useNavigation();

  const renderTabContent = () => {
    switch (currentTab) {
      case 'upload':
        return <UploadContent />;
      case 'script':
        return <ScriptGeneration />;
      case 'storyboard':
        return <StoryboardEditor />;
      case 'voice':
        return <VoiceGeneration />;
      case 'video':
        return <VideoGeneration />;
      case 'avatar':
        return <AvatarConfiguration />;
      case 'compose':
        return <VideoCompose />;
      case 'export':
        return <VideoExport />;
      default:
        return <UploadContent />;
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-100">
      <Sidebar />
      
      <main className="flex-1 flex flex-col">
        <StatusBar />
        
        <div className="flex-1 p-8 overflow-y-auto">
          <div className="animate-fade-in">
            {renderTabContent()}
          </div>
        </div>
      </main>
    </div>
  );
};

export default MainLayout;
