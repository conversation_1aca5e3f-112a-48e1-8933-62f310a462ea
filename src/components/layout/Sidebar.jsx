import { useNavigation } from '../../hooks/useNavigation';

const Sidebar = () => {
  const { tabs, currentTab, setCurrentTab, canNavigateToTab } = useNavigation();

  const handleTabClick = (tabId) => {
    if (canNavigateToTab(tabId)) {
      setCurrentTab(tabId);
    }
  };

  return (
    <nav className="w-60 bg-gray-900 text-white flex flex-col">
      {/* Logo */}
      <div className="p-5 mb-8 flex items-center gap-3">
        <img 
          src="https://images.unsplash.com/photo-1501854140801-50d01698950b?w=150&h=100&fit=crop&q=80" 
          alt="AI视频生成平台"
          className="w-8 h-8 rounded"
        />
        <h1 className="text-lg font-semibold">AI视频生成平台</h1>
      </div>

      {/* Navigation Links */}
      <div className="flex flex-col gap-1">
        {tabs.map((tab) => {
          const isActive = currentTab === tab.id;
          const canNavigate = canNavigateToTab(tab.id);
          
          return (
            <button
              key={tab.id}
              onClick={() => handleTabClick(tab.id)}
              disabled={!canNavigate}
              className={`
                flex items-center gap-3 px-5 py-3 text-left transition-all duration-300
                ${isActive 
                  ? 'bg-gray-800 text-white border-l-3 border-primary' 
                  : canNavigate
                    ? 'text-gray-300 hover:bg-gray-800 hover:text-white'
                    : 'text-gray-500 cursor-not-allowed'
                }
              `}
            >
              <span className="material-icons text-xl">{tab.icon}</span>
              <span className="font-medium">{tab.label}</span>
            </button>
          );
        })}
      </div>
    </nav>
  );
};

export default Sidebar;
