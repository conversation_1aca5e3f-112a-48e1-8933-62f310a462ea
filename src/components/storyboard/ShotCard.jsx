import { useState } from 'react';

const ShotCard = ({ shot, onRegenerate }) => {
  const [isLoading] = useState(false);

  const handleRegenerateClick = () => {
    onRegenerate(shot);
  };

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-lg hover:-translate-y-1 transition-all duration-300 group">
      {/* Mobile: Vertical Layout, Desktop: Horizontal Layout */}
      <div className="flex flex-col md:flex-row">
        {/* Left: Image Container (45% on desktop) */}
        <div className="relative bg-gray-50 h-[220px] md:h-[200px] md:w-[45%] flex-shrink-0 p-2">
          <img
            src={shot.imageUrl}
            alt={`镜头 ${shot.number}`}
            className="w-full h-full object-contain rounded-md transition-opacity duration-300"
            style={{ opacity: isLoading ? 0.5 : 1 }}
          />

          {/* Shot Number Badge */}
          <div className="absolute top-3 left-3 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-semibold shadow-lg">
            {shot.number}
          </div>

          {/* Overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <button
              onClick={handleRegenerateClick}
              className="bg-white bg-opacity-90 text-gray-800 px-4 py-2 rounded-full text-sm font-medium flex items-center gap-2 hover:bg-white transition-colors duration-200"
            >
              <span className="material-icons text-lg">auto_awesome</span>
              重新生成
            </button>
          </div>

          {/* Loading Spinner */}
          {isLoading && (
            <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center">
              <div className="w-10 h-10 border-4 border-gray-200 border-t-primary rounded-full animate-spin"></div>
            </div>
          )}
        </div>

        {/* Right: Content (55% on desktop) */}
        <div className="p-4 md:p-6 flex-1 flex flex-col justify-center">
          <div className="flex items-start gap-3">
            {/* Mobile only: Show shot number */}
            <div className="md:hidden flex-shrink-0 w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-semibold">
              {shot.number}
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-medium text-gray-900 mb-2">
                镜头 {shot.number}
              </h3>
              <p className="text-sm text-gray-600 leading-relaxed">
                {shot.scriptText}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShotCard;
