import { useEffect, useState } from 'react';
import { useApp } from '../../context/AppContext.jsx';
import { useNavigation } from '../../hooks/useNavigation';
import { useModal } from '../../hooks/useModal';
import { generateImageSeed, generateImageUrl } from '../../utils/hashUtils';
import Button from '../common/Button';
import ShotCard from './ShotCard';
import RegenerateModal from './RegenerateModal';
import ImagePreviewModal from '../common/ImagePreviewModal.jsx';

const StoryboardEditor = () => {
  const { state, dispatch, ActionTypes } = useApp();
  const { setCurrentTab } = useNavigation();
  const { isOpen, modalData, openModal, closeModal } = useModal();
  const [imagePreview, setImagePreview] = useState({ isOpen: false, shot: null });

  // Generate shots when component mounts if not already generated
  useEffect(() => {
    if (state.shots.length === 0 && state.scripts.length > 0) {
      dispatch({ type: ActionTypes.GENERATE_SHOTS });
    }
  }, [state.shots.length, state.scripts.length, dispatch, ActionTypes]);

  const handleRegenerateShot = (shot) => {
    openModal(shot);
  };

  const handleViewImage = (shot) => {
    setImagePreview({ isOpen: true, shot });
  };

  const handleCloseImagePreview = () => {
    setImagePreview({ isOpen: false, shot: null });
  };

  const handleConfirmRegenerate = (shotId, customPrompt) => {
    const shot = state.shots.find(s => s.id === shotId);
    if (!shot) return;

    const seed = generateImageSeed(shot.scriptText, customPrompt);
    const newImageUrl = generateImageUrl(seed);

    // Simulate loading delay
    setTimeout(() => {
      dispatch({
        type: ActionTypes.REGENERATE_SHOT,
        payload: { id: shotId, newImageUrl }
      });
    }, 1500);
  };

  const handleConfirmShots = () => {
    setCurrentTab('voice');
  };

  if (state.scripts.length === 0) {
    return (
      <div>
        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            镜头编辑
          </h2>
          <p className="text-gray-600">
            AI已根据您的脚本生成了以下镜头，您可以对每个镜头的图片进行调整或重新生成
          </p>
        </div>
        
        <div className="text-center py-12">
          <span className="material-icons text-6xl text-gray-300 mb-4 block">
            photo_library
          </span>
          <p className="text-gray-500 text-lg">
            请先在&quot;生成脚本&quot;页面创建脚本条目，才能生成对应的镜头。
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          镜头编辑
        </h2>
        <p className="text-gray-600">
          AI已根据您的脚本生成了以下镜头，您可以对每个镜头的图片进行调整或重新生成
        </p>
      </div>

      {/* Shot Grid */}
      <div className="flex flex-col gap-6 mb-8">
        {state.shots.map((shot) => (
          <ShotCard
            key={shot.id}
            shot={shot}
            onRegenerate={handleRegenerateShot}
            onViewImage={handleViewImage}
          />
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end">
        <Button
          variant="primary"
          onClick={handleConfirmShots}
        >
          确认镜头并生成配音
        </Button>
      </div>

      {/* Regenerate Modal */}
      <RegenerateModal
        isOpen={isOpen}
        onClose={closeModal}
        shot={modalData}
        onRegenerate={handleConfirmRegenerate}
      />

      {/* Image Preview Modal */}
      <ImagePreviewModal
        isOpen={imagePreview.isOpen}
        onClose={handleCloseImagePreview}
        imageUrl={imagePreview.shot?.imageUrl}
        title={imagePreview.shot ? `镜头 ${imagePreview.shot.number}` : ''}
        description={imagePreview.shot?.scriptText}
      />
    </div>
  );
};

export default StoryboardEditor;
